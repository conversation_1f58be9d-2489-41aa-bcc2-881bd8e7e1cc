"use strict";var M=Object.defineProperty;var E=i=>{throw TypeError(i)};var q=(i,e,t)=>e in i?M(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var c=(i,e,t)=>q(i,typeof e!="symbol"?e+"":e,t),b=(i,e,t)=>e.has(i)||E("Cannot "+t);var y=(i,e,t)=>(b(i,e,"read from private field"),t?t.call(i):e.get(i)),k=(i,e,t)=>e.has(i)?E("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(i):e.set(i,t),O=(i,e,t,r)=>(b(i,e,"write to private field"),r?r.call(i,t):e.set(i,t),t);const F=require("vscode"),L=require("os"),j=require("fs"),z=require("path"),v=require("crypto"),h=require("./sso-oidc-client-DPYFFpGi.cjs"),l=require("./errors-CP8CRW6V.cjs");require("node-machine-id");const d=require("./initialize-Ci0T2sI9.cjs"),S=require("./span-D2eRFwI2.cjs");require("@opentelemetry/api");const $=require("http");function T(i){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(i){for(const t in i)if(t!=="default"){const r=Object.getOwnPropertyDescriptor(i,t);Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>i[t]})}}return e.default=i,Object.freeze(e)}const R=T(F),H=T(L),f=T(j),_=T(z);class U extends Error{constructor(){super("AuthSSOServer: missing auth server port")}}class B extends Error{constructor(){super("AuthSSOServer: missing code")}}class J extends Error{constructor(){super("AuthSSOServer: missing state")}}class V extends Error{constructor(){super("AuthSSOServer: invalid state")}}class W extends Error{constructor(){super("AuthSSOServer: timeout")}}class G extends Error{constructor(e,t){super(`AuthSSOServer: ${e}: ${t}`)}}var p;const g=class g{constructor(e){c(this,"baseUrl","http://127.0.0.1");c(this,"oauthCallback","/oauth/callback");c(this,"authenticationFlowTimeoutInMs",6e5);c(this,"authenticationWarningTimeoutInMs",6e4);c(this,"listenTimeoutMs",1e4);c(this,"authenticationPromise");c(this,"deferred");c(this,"server");c(this,"connections");c(this,"_closed",!1);this.state=e,this.authenticationPromise=new Promise((t,r)=>{this.deferred={resolve:t,reject:r}}),this.connections=[],this.server=$.createServer((t,r)=>{if(r.setHeader("Access-Control-Allow-Methods","GET"),!t.url)return;const s=new URL(t.url,this.baseUrl);switch(s.pathname){case this.oauthCallback:{this.handleAuthentication(s.searchParams,r);break}default:l.logger.error("Failed to close already existing auth server in AuthSSOServer.init(): %s")}}),this.server.on("connection",t=>{this.connections.push(t)})}static get lastInstance(){return y(g,p)}static init(e=""){return S.withSpan(d.TelemetryNamespace.Auth,"auth-server.init",async()=>{const t=y(g,p);if(t!==void 0&&!t.closed)try{await t.close()}catch(s){l.logger.error("Failed to close already existing auth server in AuthSSOServer.init(): %s",s)}l.logger.debug("AuthSSOServer: Initialized new auth server.");const r=new g(e);return O(g,p,r),r})}start(e){return S.withSpan(d.TelemetryNamespace.Auth,"auth-server.start",async t=>{let r=0;if(this.server.listening)throw new Error("AuthSSOServer: Server already started");return new Promise((s,a)=>{this.server.on("close",()=>{a(new Error("AuthSSOServer: Server has closed"))}),this.server.on("error",o=>{o.code==="EADDRINUSE"&&e&&e.length>r+1?this.listen(e[++r]):a(new Error(`AuthSSOServer: Server failed: ${o.message}`))});const n=setTimeout(()=>{this.server.listening||(this.close(),a(new W))},this.listenTimeoutMs);this.server.on("listening",()=>{clearTimeout(n),this.server.address()||a(new U),t.setAttribute("redirectUri",this.redirectUri),s()}),this.listen((e==null?void 0:e[0])||0)})})}listen(e){this.server.listen(e,"127.0.0.1")}close(){return S.withSpan(d.TelemetryNamespace.Auth,"auth-server.close",async e=>{try{e.setAttribute("redirectUri",this.redirectUri)}catch{e.setAttribute("redirectUri","")}return new Promise((t,r)=>{if(this._closed){t();return}this.server.listening||r(new Error("AuthSSOServer: Server not started")),l.logger.debug("AuthSSOServer: Attempting to close server.");for(const s of this.connections)s.destroy();this.server.close(s=>{s&&r(s),this._closed=!0,l.logger.debug("AuthSSOServer: Server closed successfully."),t()})})})}get redirectUri(){return`${this.baseLocation}${this.oauthCallback}`}get baseLocation(){return`${this.baseUrl}:${this.getPort()}`}get closed(){return this._closed}getAddress(){return this.server.address()}getPort(){const e=this.getAddress();if(e instanceof Object)return e.port;if(typeof e=="string")return Number.parseInt(e);throw new U}redirect(e,t){e.writeHead(200,{"Content-Type":"text/html"});let r="You can close this window";t&&(r+=`<br>Error: ${t}`),e.write(r),e.end()}handleAuthentication(e,t){var n;const r=e.get("error"),s=e.get("error_description");if(r&&s){this.handleRequestRejection(t,new G(r,s));return}const a=e.get("code");if(!a){this.handleRequestRejection(t,new B);return}if(this.state){const o=e.get("state");if(!o){this.handleRequestRejection(t,new J);return}if(o!==this.state){this.handleRequestRejection(t,new V);return}}(n=this.deferred)==null||n.resolve(a),this.redirect(t)}handleRequestRejection(e,t){var r;this.redirect(e,t.message),(r=this.deferred)==null||r.reject(t.message)}cancelCurrentFlow(){var e;l.logger.debug("AuthSSOServer: Canceling current login flow"),(e=this.deferred)==null||e.reject("user cancellation")}async waitForAuthorization(){const e=setTimeout(()=>{l.logger.warn("AuthSSOServer: Authentication is taking a long time")},this.authenticationWarningTimeoutInMs),t=await Promise.race([this.authenticationPromise,new Promise((r,s)=>{setTimeout(()=>{s(new h.AbandonedError("Timed-out waiting for browser login flow to complete"))},this.authenticationFlowTimeoutInMs)})]);return clearTimeout(e),t}};p=new WeakMap,k(g,p);let A=g;const K=15*60,Y="https://view.awsapps.com/start",Q="https://amzn.awsapps.com/start";class x{constructor(){c(this,"cacheDirectory",_.join(H.homedir(),".aws","sso","cache"))}getClientRegistrationPath(e){return _.join(this.cacheDirectory,`${e}.json`)}ensureCacheDirectory(){f.existsSync(this.cacheDirectory)||f.mkdirSync(this.cacheDirectory,{recursive:!0})}writeClientRegistration(e,t){this.ensureCacheDirectory();const r=this.getClientRegistrationPath(e);f.writeFileSync(r,JSON.stringify(t,void 0,2))}readClientRegistration(e){const t=this.getClientRegistrationPath(e);if(f.existsSync(t)){const r=f.readFileSync(t,"utf8");return JSON.parse(r)}}}class X{constructor(){c(this,"storage");c(this,"scopes",["codewhisperer:completions","codewhisperer:analysis","codewhisperer:conversations","codewhisperer:transformations","codewhisperer:taskassist"]);c(this,"authServer");this.storage=new x}getClientIdHash(e){return v.createHash("sha1").update(JSON.stringify({startUrl:e})).digest("hex")}tokenResponseToToken(e,t,r,s){const a=new Date,n=new Date(a.getTime()+Number(e.expiresIn)*1e3);return{accessToken:e.accessToken,refreshToken:e.refreshToken,expiresAt:n.toISOString(),clientIdHash:t,authMethod:"IdC",provider:r,region:s}}isClientRegistrationExpired(e){if(!e.expiresAt)return!0;const t=new Date(e.expiresAt),r=new Date;return t.valueOf()<r.valueOf()+K*1e3}async registerClient(e,t,r=!1){const s=this.getClientIdHash(e),a=this.storage.readClientRegistration(s),n=new h.SSOOIDCClient(t);if(a&&!this.isClientRegistrationExpired(a))return a;const o=await n.registerClient({clientName:"Kiro IDE",clientType:"public",scopes:this.scopes,grantTypes:["authorization_code","refresh_token"],redirectUris:["http://127.0.0.1/oauth/callback"],issuerUrl:e},r),u={clientId:o.clientId,clientSecret:o.clientSecret,expiresAt:new Date(o.clientSecretExpiresAt*1e3).toISOString()};return this.storage.writeClientRegistration(s,u),u}getStartUrl(e){return e.provider==="Enterprise"?e.startUrl:e.provider==="BuilderId"?Y:Q}async authenticate(e){return S.withSpan(d.TelemetryNamespace.Auth,"idc-provider.authenticate",async t=>{if(t.setAttribute("authProvider",e.provider),e.authMethod!=="IdC")throw new h.UnexpectedIssueError("IdC auth: invalid auth method");const r=this.getStartUrl(e),s=e.region||"us-east-1",a=new h.SSOOIDCClient(s),n=await this.registerClient(r,s,e.provider==="Enterprise"),o=v.randomUUID();this.authServer=await A.init(o),await this.authServer.start();const u=v.randomBytes(32).toString("base64url"),w=v.createHash("sha256").update(u).digest().toString("base64url"),m=this.authServer.redirectUri,P=new URLSearchParams({response_type:"code",client_id:n.clientId,redirect_uri:m,scopes:this.scopes.join(","),state:o,code_challenge:w,code_challenge_method:"S256"}),D=`https://oidc.${s}.amazonaws.com/authorize?${P.toString()}`;await R.env.openExternal(R.Uri.parse(D));let C;try{C=await this.authServer.waitForAuthorization()}catch(I){throw I==="user cancellation"?new h.CanceledError("Sign-in flow canceled by the user"):I}finally{this.authServer=void 0}const N=await a.createToken({clientId:n.clientId,clientSecret:n.clientSecret,grantType:"authorization_code",redirectUri:m,code:C,codeVerifier:u});return this.tokenResponseToToken(N,this.getClientIdHash(r),e.provider,s)})}cancelAuth(){this.authServer&&this.authServer.cancelCurrentFlow()}async refreshToken(e){return S.withSpan(d.TelemetryNamespace.Auth,"idc-provider.refreshToken",async t=>{if(t.setAttribute("authProvider",e.provider),e.authMethod!=="IdC")throw new h.UnexpectedIssueError("IdC auth: invalid auth method");const{refreshToken:r,clientIdHash:s,provider:a,region:n}=e;try{const o=this.storage.readClientRegistration(s),u=n||"us-east-1",w=new h.SSOOIDCClient(u);if(!o||this.isClientRegistrationExpired(o))throw new h.InvalidIdCAuthError("IdC auth: No valid client registration found");const m=await w.createToken({clientId:o.clientId,clientSecret:o.clientSecret,refreshToken:r,grantType:"refresh_token"});return this.tokenResponseToToken(m,s,a,u)}catch(o){throw l.logger.error("Error refreshing token:",o),o}})}logout(e){return Promise.resolve()}deleteAccount(e){throw new Error("Account deletion not supported for enterprise auth")}}const Z=[3128,4649,6588,8008,9091];class ee{constructor(){c(this,"authServiceClient");c(this,"authServer");this.authServiceClient=new h.AuthServiceClient}tokenResponseToToken(e,t){const r=new Date,s=new Date(r.getTime()+Number(e.expiresIn)*1e3);return{accessToken:e.accessToken,refreshToken:e.refreshToken,profileArn:e.profileArn,expiresAt:s.toISOString(),authMethod:"social",provider:t}}async authenticate(e){return S.withSpan(d.TelemetryNamespace.Auth,"social-provider.authenticate",async t=>{if(t.setAttribute("authProvider",e.provider),e.authMethod!=="social")throw new h.UnexpectedIssueError("Social auth: invalid auth method");const r=v.randomUUID();this.authServer=await A.init(r),await this.authServer.start(Z);const s=v.randomBytes(32).toString("base64url"),a=v.createHash("sha256").update(s).digest().toString("base64url"),n=this.authServer.redirectUri.replace("127.0.0.1","localhost");await this.authServiceClient.login({provider:e.provider,redirectUri:n,codeChallenge:a,state:r});let o;try{o=await this.authServer.waitForAuthorization()}catch(w){throw w==="user cancellation"?new h.CanceledError("Sign-in flow canceled by the user"):w}finally{this.authServer=void 0}const u=await this.authServiceClient.createToken({code:o,codeVerifier:s,redirectUri:n});return this.tokenResponseToToken(u,e.provider)})}cancelAuth(){this.authServer&&this.authServer.cancelCurrentFlow()}async refreshToken(e){return S.withSpan(d.TelemetryNamespace.Auth,"social-provider.refreshToken",async t=>{if(t.setAttribute("authProvider",e.provider),e.authMethod!=="social")throw new h.UnexpectedIssueError("Social auth: invalid auth method");const{refreshToken:r,profileArn:s,provider:a}=e;try{const n=await this.authServiceClient.refreshToken({refreshToken:r});return n.profileArn=s,this.tokenResponseToToken(n,a)}catch(n){throw l.logger.error("Error refreshing token:",n),n}})}async logout(e){return S.withSpan(d.TelemetryNamespace.Auth,"social-provider.logout",async t=>{if(t.setAttribute("authProvider",e.provider),!!e.refreshToken)return this.authServiceClient.logout({refreshToken:e.refreshToken})})}async deleteAccount(e){return S.withSpan(d.TelemetryNamespace.Auth,"social-provider.deleteAccount",async t=>(t.setAttribute("authProvider",e.provider),this.authServiceClient.deleteAccount(e.accessToken)))}}exports.ClientRegistrationStorage=x;exports.IDCAuthProvider=X;exports.SocialAuthProvider=ee;
