{"name": "@kiro/kiricons", "publisher": "kiro", "description": "Icon set for <PERSON><PERSON>", "version": "0.1.0", "private": true, "license": "AWS-IPL", "exports": {"./styles.css": "./dist/kiricon.css"}, "files": ["dist"], "devDependencies": {"@twbs/fantasticon": "^3.1.0", "rimraf": "^3.0.2", "svgo": "^3.3.2"}, "scripts": {"clean": "rimraf ./dist", "svgo": "svgo -f ./src/ --config svgo.config.js", "build": "npm run clean && npm run svgo && node build.js"}}