{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"AlertHook": {"description": "Hook interface for alert actions.", "properties": {"message": {"type": "string"}, "type": {"const": "alert", "type": "string"}}, "type": "object"}, "AskAgentHook": {"description": "Hook interface for agent query actions.", "properties": {"prompt": {"type": "string"}, "promptId": {"type": "string"}, "type": {"const": "askAgent", "type": "string"}}, "type": "object"}, "FileCreatedHook": {"description": "Hook interface for file creation events.", "properties": {"pattern": {"type": "string"}, "type": {"const": "fileCreated", "type": "string"}}, "type": "object"}, "FileDeletedHook": {"description": "Hook interface for file deletion events.", "properties": {"pattern": {"type": "string"}, "type": {"const": "fileDeleted", "type": "string"}}, "type": "object"}, "FileEditedHook": {"description": "Hook interface for file editing events.", "properties": {"pattern": {"type": "string"}, "scopeId": {"type": "string"}, "type": {"const": "fileEdited", "type": "string"}}, "type": "object"}, "ThenWorkspaceHook": {"anyOf": [{"$ref": "#/definitions/AlertHook"}, {"$ref": "#/definitions/AskAgentHook"}], "description": "Union type representing all possible actions that can be executed when a hook is triggered."}, "UserTriggeredHook": {"description": "Hook interface for user-triggered events.", "properties": {"type": {"const": "userTriggered", "type": "string"}}, "type": "object"}, "WhenWorkspaceHook": {"anyOf": [{"$ref": "#/definitions/FileEditedHook"}, {"$ref": "#/definitions/UserTriggeredHook"}, {"$ref": "#/definitions/FileCreatedHook"}, {"$ref": "#/definitions/FileDeletedHook"}], "description": "Union type representing all possible trigger conditions for a workspace hook."}}, "description": "When defined on disk, id is option and in that case is file path", "properties": {"comment": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "then": {"$ref": "#/definitions/ThenWorkspaceHook"}, "when": {"$ref": "#/definitions/WhenWorkspaceHook"}}, "type": "object"}