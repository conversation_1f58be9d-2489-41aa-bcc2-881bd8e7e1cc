"use strict";var Br=Object.defineProperty;var Wr=(e,t,n)=>t in e?Br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var B=(e,t,n)=>Wr(e,typeof t!="symbol"?t+"":t,n);const zr=require("@opentelemetry/exporter-metrics-otlp-http"),Yr=require("@opentelemetry/exporter-trace-otlp-http"),Kr=require("@opentelemetry/id-generator-aws-xray"),Zr=require("@opentelemetry/propagator-aws-xray"),vt=require("@opentelemetry/resources"),Y=require("@opentelemetry/sdk-metrics"),Jr=require("@opentelemetry/sdk-node"),Qr=require("@opentelemetry/sdk-trace-base"),C=require("@opentelemetry/api"),Tt=require("@opentelemetry/semantic-conventions"),en=require("vscode"),tn=require("node-machine-id"),X=require("./errors-CP8CRW6V.cjs");require("path");const rn=require("os");require("fs");function mr(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const n in e)if(n!=="default"){const s=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,s.get?s:{enumerable:!0,get:()=>e[n]})}}return t.default=e,Object.freeze(t)}const ue=mr(en),ce=mr(rn);class nn{constructor(t){B(this,"keys");this.keys=t.keys}onStart(t,n){const s=C.propagation.getBaggage(n);s&&this.keys.forEach(i=>{const u=s.getEntry(i);u&&t.setAttribute(i,u.value)})}onEnd(t){}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}}var Q=(e=>(e.Onboarding="kiro.onboarding",e.Auth="kiro.auth",e.Session="kiro.session",e.Feature="kiro.feature",e.User="kiro.user",e.Tool="kiro.tool",e.Operation="kiro.operation",e.Periodic="kiro.periodic",e))(Q||{}),oe={exports:{}},Ie,bt;function le(){if(bt)return Ie;bt=1;const e="2.0.0",t=256,n=Number.MAX_SAFE_INTEGER||9007199254740991,s=16,i=t-6;return Ie={MAX_LENGTH:t,MAX_SAFE_COMPONENT_LENGTH:s,MAX_SAFE_BUILD_LENGTH:i,MAX_SAFE_INTEGER:n,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:e,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2},Ie}var $e,At;function fe(){return At||(At=1,$e=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...t)=>console.error("SEMVER",...t):()=>{}),$e}var St;function ie(){return St||(St=1,function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:i}=le(),u=fe();t=e.exports={};const a=t.re=[],f=t.safeRe=[],c=t.src=[],o=t.safeSrc=[],r=t.t={};let l=0;const h="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",i],[h,s]],I=T=>{for(const[O,N]of p)T=T.split(`${O}*`).join(`${O}{0,${N}}`).split(`${O}+`).join(`${O}{1,${N}}`);return T},d=(T,O,N)=>{const L=I(O),_=l++;u(T,_,O),r[T]=_,c[_]=O,o[_]=L,a[_]=new RegExp(O,N?"g":void 0),f[_]=new RegExp(L,N?"g":void 0)};d("NUMERICIDENTIFIER","0|[1-9]\\d*"),d("NUMERICIDENTIFIERLOOSE","\\d+"),d("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),d("MAINVERSION",`(${c[r.NUMERICIDENTIFIER]})\\.(${c[r.NUMERICIDENTIFIER]})\\.(${c[r.NUMERICIDENTIFIER]})`),d("MAINVERSIONLOOSE",`(${c[r.NUMERICIDENTIFIERLOOSE]})\\.(${c[r.NUMERICIDENTIFIERLOOSE]})\\.(${c[r.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASEIDENTIFIER",`(?:${c[r.NONNUMERICIDENTIFIER]}|${c[r.NUMERICIDENTIFIER]})`),d("PRERELEASEIDENTIFIERLOOSE",`(?:${c[r.NONNUMERICIDENTIFIER]}|${c[r.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASE",`(?:-(${c[r.PRERELEASEIDENTIFIER]}(?:\\.${c[r.PRERELEASEIDENTIFIER]})*))`),d("PRERELEASELOOSE",`(?:-?(${c[r.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[r.PRERELEASEIDENTIFIERLOOSE]})*))`),d("BUILDIDENTIFIER",`${h}+`),d("BUILD",`(?:\\+(${c[r.BUILDIDENTIFIER]}(?:\\.${c[r.BUILDIDENTIFIER]})*))`),d("FULLPLAIN",`v?${c[r.MAINVERSION]}${c[r.PRERELEASE]}?${c[r.BUILD]}?`),d("FULL",`^${c[r.FULLPLAIN]}$`),d("LOOSEPLAIN",`[v=\\s]*${c[r.MAINVERSIONLOOSE]}${c[r.PRERELEASELOOSE]}?${c[r.BUILD]}?`),d("LOOSE",`^${c[r.LOOSEPLAIN]}$`),d("GTLT","((?:<|>)?=?)"),d("XRANGEIDENTIFIERLOOSE",`${c[r.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),d("XRANGEIDENTIFIER",`${c[r.NUMERICIDENTIFIER]}|x|X|\\*`),d("XRANGEPLAIN",`[v=\\s]*(${c[r.XRANGEIDENTIFIER]})(?:\\.(${c[r.XRANGEIDENTIFIER]})(?:\\.(${c[r.XRANGEIDENTIFIER]})(?:${c[r.PRERELEASE]})?${c[r.BUILD]}?)?)?`),d("XRANGEPLAINLOOSE",`[v=\\s]*(${c[r.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[r.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[r.XRANGEIDENTIFIERLOOSE]})(?:${c[r.PRERELEASELOOSE]})?${c[r.BUILD]}?)?)?`),d("XRANGE",`^${c[r.GTLT]}\\s*${c[r.XRANGEPLAIN]}$`),d("XRANGELOOSE",`^${c[r.GTLT]}\\s*${c[r.XRANGEPLAINLOOSE]}$`),d("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),d("COERCE",`${c[r.COERCEPLAIN]}(?:$|[^\\d])`),d("COERCEFULL",c[r.COERCEPLAIN]+`(?:${c[r.PRERELEASE]})?(?:${c[r.BUILD]})?(?:$|[^\\d])`),d("COERCERTL",c[r.COERCE],!0),d("COERCERTLFULL",c[r.COERCEFULL],!0),d("LONETILDE","(?:~>?)"),d("TILDETRIM",`(\\s*)${c[r.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",d("TILDE",`^${c[r.LONETILDE]}${c[r.XRANGEPLAIN]}$`),d("TILDELOOSE",`^${c[r.LONETILDE]}${c[r.XRANGEPLAINLOOSE]}$`),d("LONECARET","(?:\\^)"),d("CARETTRIM",`(\\s*)${c[r.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",d("CARET",`^${c[r.LONECARET]}${c[r.XRANGEPLAIN]}$`),d("CARETLOOSE",`^${c[r.LONECARET]}${c[r.XRANGEPLAINLOOSE]}$`),d("COMPARATORLOOSE",`^${c[r.GTLT]}\\s*(${c[r.LOOSEPLAIN]})$|^$`),d("COMPARATOR",`^${c[r.GTLT]}\\s*(${c[r.FULLPLAIN]})$|^$`),d("COMPARATORTRIM",`(\\s*)${c[r.GTLT]}\\s*(${c[r.LOOSEPLAIN]}|${c[r.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",d("HYPHENRANGE",`^\\s*(${c[r.XRANGEPLAIN]})\\s+-\\s+(${c[r.XRANGEPLAIN]})\\s*$`),d("HYPHENRANGELOOSE",`^\\s*(${c[r.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[r.XRANGEPLAINLOOSE]})\\s*$`),d("STAR","(<|>)?=?\\s*\\*"),d("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),d("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}(oe,oe.exports)),oe.exports}var ve,Ot;function ht(){if(Ot)return ve;Ot=1;const e=Object.freeze({loose:!0}),t=Object.freeze({});return ve=s=>s?typeof s!="object"?e:s:t,ve}var Te,Lt;function gr(){if(Lt)return Te;Lt=1;const e=/^[0-9]+$/,t=(s,i)=>{const u=e.test(s),a=e.test(i);return u&&a&&(s=+s,i=+i),s===i?0:u&&!a?-1:a&&!u?1:s<i?-1:1};return Te={compareIdentifiers:t,rcompareIdentifiers:(s,i)=>t(i,s)},Te}var be,Ct;function G(){if(Ct)return be;Ct=1;const e=fe(),{MAX_LENGTH:t,MAX_SAFE_INTEGER:n}=le(),{safeRe:s,t:i}=ie(),u=ht(),{compareIdentifiers:a}=gr();class f{constructor(o,r){if(r=u(r),o instanceof f){if(o.loose===!!r.loose&&o.includePrerelease===!!r.includePrerelease)return o;o=o.version}else if(typeof o!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof o}".`);if(o.length>t)throw new TypeError(`version is longer than ${t} characters`);e("SemVer",o,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;const l=o.trim().match(r.loose?s[i.LOOSE]:s[i.FULL]);if(!l)throw new TypeError(`Invalid Version: ${o}`);if(this.raw=o,this.major=+l[1],this.minor=+l[2],this.patch=+l[3],this.major>n||this.major<0)throw new TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw new TypeError("Invalid patch version");l[4]?this.prerelease=l[4].split(".").map(h=>{if(/^[0-9]+$/.test(h)){const p=+h;if(p>=0&&p<n)return p}return h}):this.prerelease=[],this.build=l[5]?l[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(o){if(e("SemVer.compare",this.version,this.options,o),!(o instanceof f)){if(typeof o=="string"&&o===this.version)return 0;o=new f(o,this.options)}return o.version===this.version?0:this.compareMain(o)||this.comparePre(o)}compareMain(o){return o instanceof f||(o=new f(o,this.options)),a(this.major,o.major)||a(this.minor,o.minor)||a(this.patch,o.patch)}comparePre(o){if(o instanceof f||(o=new f(o,this.options)),this.prerelease.length&&!o.prerelease.length)return-1;if(!this.prerelease.length&&o.prerelease.length)return 1;if(!this.prerelease.length&&!o.prerelease.length)return 0;let r=0;do{const l=this.prerelease[r],h=o.prerelease[r];if(e("prerelease compare",r,l,h),l===void 0&&h===void 0)return 0;if(h===void 0)return 1;if(l===void 0)return-1;if(l===h)continue;return a(l,h)}while(++r)}compareBuild(o){o instanceof f||(o=new f(o,this.options));let r=0;do{const l=this.build[r],h=o.build[r];if(e("build compare",r,l,h),l===void 0&&h===void 0)return 0;if(h===void 0)return 1;if(l===void 0)return-1;if(l===h)continue;return a(l,h)}while(++r)}inc(o,r,l){if(o.startsWith("pre")){if(!r&&l===!1)throw new Error("invalid increment argument: identifier is empty");if(r){const h=`-${r}`.match(this.options.loose?s[i.PRERELEASELOOSE]:s[i.PRERELEASE]);if(!h||h[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(o){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,l);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,l);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,l),this.inc("pre",r,l);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,l),this.inc("pre",r,l);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const h=Number(l)?1:0;if(this.prerelease.length===0)this.prerelease=[h];else{let p=this.prerelease.length;for(;--p>=0;)typeof this.prerelease[p]=="number"&&(this.prerelease[p]++,p=-2);if(p===-1){if(r===this.prerelease.join(".")&&l===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(h)}}if(r){let p=[r,h];l===!1&&(p=[r]),a(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=p):this.prerelease=p}break}default:throw new Error(`invalid increment argument: ${o}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}return be=f,be}var Ae,wt;function ee(){if(wt)return Ae;wt=1;const e=G();return Ae=(n,s,i=!1)=>{if(n instanceof e)return n;try{return new e(n,s)}catch(u){if(!i)return null;throw u}},Ae}var Se,Nt;function sn(){if(Nt)return Se;Nt=1;const e=ee();return Se=(n,s)=>{const i=e(n,s);return i?i.version:null},Se}var Oe,Pt;function on(){if(Pt)return Oe;Pt=1;const e=ee();return Oe=(n,s)=>{const i=e(n.trim().replace(/^[=v]+/,""),s);return i?i.version:null},Oe}var Le,yt;function an(){if(yt)return Le;yt=1;const e=G();return Le=(n,s,i,u,a)=>{typeof i=="string"&&(a=u,u=i,i=void 0);try{return new e(n instanceof e?n.version:n,i).inc(s,u,a).version}catch{return null}},Le}var Ce,_t;function cn(){if(_t)return Ce;_t=1;const e=ee();return Ce=(n,s)=>{const i=e(n,null,!0),u=e(s,null,!0),a=i.compare(u);if(a===0)return null;const f=a>0,c=f?i:u,o=f?u:i,r=!!c.prerelease.length;if(!!o.prerelease.length&&!r){if(!o.patch&&!o.minor)return"major";if(o.compareMain(c)===0)return o.minor&&!o.patch?"minor":"patch"}const h=r?"pre":"";return i.major!==u.major?h+"major":i.minor!==u.minor?h+"minor":i.patch!==u.patch?h+"patch":"prerelease"},Ce}var we,qt;function un(){if(qt)return we;qt=1;const e=G();return we=(n,s)=>new e(n,s).major,we}var Ne,kt;function ln(){if(kt)return Ne;kt=1;const e=G();return Ne=(n,s)=>new e(n,s).minor,Ne}var Pe,Ft;function fn(){if(Ft)return Pe;Ft=1;const e=G();return Pe=(n,s)=>new e(n,s).patch,Pe}var ye,Dt;function hn(){if(Dt)return ye;Dt=1;const e=ee();return ye=(n,s)=>{const i=e(n,s);return i&&i.prerelease.length?i.prerelease:null},ye}var _e,Mt;function j(){if(Mt)return _e;Mt=1;const e=G();return _e=(n,s,i)=>new e(n,i).compare(new e(s,i)),_e}var qe,Gt;function dn(){if(Gt)return qe;Gt=1;const e=j();return qe=(n,s,i)=>e(s,n,i),qe}var ke,xt;function pn(){if(xt)return ke;xt=1;const e=j();return ke=(n,s)=>e(n,s,!0),ke}var Fe,jt;function dt(){if(jt)return Fe;jt=1;const e=G();return Fe=(n,s,i)=>{const u=new e(n,i),a=new e(s,i);return u.compare(a)||u.compareBuild(a)},Fe}var De,Ut;function En(){if(Ut)return De;Ut=1;const e=dt();return De=(n,s)=>n.sort((i,u)=>e(i,u,s)),De}var Me,Xt;function mn(){if(Xt)return Me;Xt=1;const e=dt();return Me=(n,s)=>n.sort((i,u)=>e(u,i,s)),Me}var Ge,Vt;function he(){if(Vt)return Ge;Vt=1;const e=j();return Ge=(n,s,i)=>e(n,s,i)>0,Ge}var xe,Ht;function pt(){if(Ht)return xe;Ht=1;const e=j();return xe=(n,s,i)=>e(n,s,i)<0,xe}var je,Bt;function Rr(){if(Bt)return je;Bt=1;const e=j();return je=(n,s,i)=>e(n,s,i)===0,je}var Ue,Wt;function Ir(){if(Wt)return Ue;Wt=1;const e=j();return Ue=(n,s,i)=>e(n,s,i)!==0,Ue}var Xe,zt;function Et(){if(zt)return Xe;zt=1;const e=j();return Xe=(n,s,i)=>e(n,s,i)>=0,Xe}var Ve,Yt;function mt(){if(Yt)return Ve;Yt=1;const e=j();return Ve=(n,s,i)=>e(n,s,i)<=0,Ve}var He,Kt;function $r(){if(Kt)return He;Kt=1;const e=Rr(),t=Ir(),n=he(),s=Et(),i=pt(),u=mt();return He=(f,c,o,r)=>{switch(c){case"===":return typeof f=="object"&&(f=f.version),typeof o=="object"&&(o=o.version),f===o;case"!==":return typeof f=="object"&&(f=f.version),typeof o=="object"&&(o=o.version),f!==o;case"":case"=":case"==":return e(f,o,r);case"!=":return t(f,o,r);case">":return n(f,o,r);case">=":return s(f,o,r);case"<":return i(f,o,r);case"<=":return u(f,o,r);default:throw new TypeError(`Invalid operator: ${c}`)}},He}var Be,Zt;function gn(){if(Zt)return Be;Zt=1;const e=G(),t=ee(),{safeRe:n,t:s}=ie();return Be=(u,a)=>{if(u instanceof e)return u;if(typeof u=="number"&&(u=String(u)),typeof u!="string")return null;a=a||{};let f=null;if(!a.rtl)f=u.match(a.includePrerelease?n[s.COERCEFULL]:n[s.COERCE]);else{const p=a.includePrerelease?n[s.COERCERTLFULL]:n[s.COERCERTL];let I;for(;(I=p.exec(u))&&(!f||f.index+f[0].length!==u.length);)(!f||I.index+I[0].length!==f.index+f[0].length)&&(f=I),p.lastIndex=I.index+I[1].length+I[2].length;p.lastIndex=-1}if(f===null)return null;const c=f[2],o=f[3]||"0",r=f[4]||"0",l=a.includePrerelease&&f[5]?`-${f[5]}`:"",h=a.includePrerelease&&f[6]?`+${f[6]}`:"";return t(`${c}.${o}.${r}${l}${h}`,a)},Be}var We,Jt;function Rn(){if(Jt)return We;Jt=1;class e{constructor(){this.max=1e3,this.map=new Map}get(n){const s=this.map.get(n);if(s!==void 0)return this.map.delete(n),this.map.set(n,s),s}delete(n){return this.map.delete(n)}set(n,s){if(!this.delete(n)&&s!==void 0){if(this.map.size>=this.max){const u=this.map.keys().next().value;this.delete(u)}this.map.set(n,s)}return this}}return We=e,We}var ze,Qt;function U(){if(Qt)return ze;Qt=1;const e=/\s+/g;class t{constructor(E,$){if($=i($),E instanceof t)return E.loose===!!$.loose&&E.includePrerelease===!!$.includePrerelease?E:new t(E.raw,$);if(E instanceof u)return this.raw=E.value,this.set=[[E]],this.formatted=void 0,this;if(this.options=$,this.loose=!!$.loose,this.includePrerelease=!!$.includePrerelease,this.raw=E.trim().replace(e," "),this.set=this.raw.split("||").map(g=>this.parseRange(g.trim())).filter(g=>g.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const g=this.set[0];if(this.set=this.set.filter(v=>!d(v[0])),this.set.length===0)this.set=[g];else if(this.set.length>1){for(const v of this.set)if(v.length===1&&T(v[0])){this.set=[v];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let E=0;E<this.set.length;E++){E>0&&(this.formatted+="||");const $=this.set[E];for(let g=0;g<$.length;g++)g>0&&(this.formatted+=" "),this.formatted+=$[g].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(E){const g=((this.options.includePrerelease&&p)|(this.options.loose&&I))+":"+E,v=s.get(g);if(v)return v;const R=this.options.loose,b=R?c[o.HYPHENRANGELOOSE]:c[o.HYPHENRANGE];E=E.replace(b,ge(this.options.includePrerelease)),a("hyphen replace",E),E=E.replace(c[o.COMPARATORTRIM],r),a("comparator trim",E),E=E.replace(c[o.TILDETRIM],l),a("tilde trim",E),E=E.replace(c[o.CARETTRIM],h),a("caret trim",E);let w=E.split(" ").map(q=>N(q,this.options)).join(" ").split(/\s+/).map(q=>me(q,this.options));R&&(w=w.filter(q=>(a("loose invalid filter",q,this.options),!!q.match(c[o.COMPARATORLOOSE])))),a("range list",w);const S=new Map,P=w.map(q=>new u(q,this.options));for(const q of P){if(d(q))return[q];S.set(q.value,q)}S.size>1&&S.has("")&&S.delete("");const D=[...S.values()];return s.set(g,D),D}intersects(E,$){if(!(E instanceof t))throw new TypeError("a Range is required");return this.set.some(g=>O(g,$)&&E.set.some(v=>O(v,$)&&g.every(R=>v.every(b=>R.intersects(b,$)))))}test(E){if(!E)return!1;if(typeof E=="string")try{E=new f(E,this.options)}catch{return!1}for(let $=0;$<this.set.length;$++)if(Re(this.set[$],E,this.options))return!0;return!1}}ze=t;const n=Rn(),s=new n,i=ht(),u=de(),a=fe(),f=G(),{safeRe:c,t:o,comparatorTrimReplace:r,tildeTrimReplace:l,caretTrimReplace:h}=ie(),{FLAG_INCLUDE_PRERELEASE:p,FLAG_LOOSE:I}=le(),d=m=>m.value==="<0.0.0-0",T=m=>m.value==="",O=(m,E)=>{let $=!0;const g=m.slice();let v=g.pop();for(;$&&g.length;)$=g.every(R=>v.intersects(R,E)),v=g.pop();return $},N=(m,E)=>(a("comp",m,E),m=y(m,E),a("caret",m),m=_(m,E),a("tildes",m),m=A(m,E),a("xrange",m),m=Ee(m,E),a("stars",m),m),L=m=>!m||m.toLowerCase()==="x"||m==="*",_=(m,E)=>m.trim().split(/\s+/).map($=>F($,E)).join(" "),F=(m,E)=>{const $=E.loose?c[o.TILDELOOSE]:c[o.TILDE];return m.replace($,(g,v,R,b,w)=>{a("tilde",m,g,v,R,b,w);let S;return L(v)?S="":L(R)?S=`>=${v}.0.0 <${+v+1}.0.0-0`:L(b)?S=`>=${v}.${R}.0 <${v}.${+R+1}.0-0`:w?(a("replaceTilde pr",w),S=`>=${v}.${R}.${b}-${w} <${v}.${+R+1}.0-0`):S=`>=${v}.${R}.${b} <${v}.${+R+1}.0-0`,a("tilde return",S),S})},y=(m,E)=>m.trim().split(/\s+/).map($=>k($,E)).join(" "),k=(m,E)=>{a("caret",m,E);const $=E.loose?c[o.CARETLOOSE]:c[o.CARET],g=E.includePrerelease?"-0":"";return m.replace($,(v,R,b,w,S)=>{a("caret",m,v,R,b,w,S);let P;return L(R)?P="":L(b)?P=`>=${R}.0.0${g} <${+R+1}.0.0-0`:L(w)?R==="0"?P=`>=${R}.${b}.0${g} <${R}.${+b+1}.0-0`:P=`>=${R}.${b}.0${g} <${+R+1}.0.0-0`:S?(a("replaceCaret pr",S),R==="0"?b==="0"?P=`>=${R}.${b}.${w}-${S} <${R}.${b}.${+w+1}-0`:P=`>=${R}.${b}.${w}-${S} <${R}.${+b+1}.0-0`:P=`>=${R}.${b}.${w}-${S} <${+R+1}.0.0-0`):(a("no pr"),R==="0"?b==="0"?P=`>=${R}.${b}.${w}${g} <${R}.${b}.${+w+1}-0`:P=`>=${R}.${b}.${w}${g} <${R}.${+b+1}.0-0`:P=`>=${R}.${b}.${w} <${+R+1}.0.0-0`),a("caret return",P),P})},A=(m,E)=>(a("replaceXRanges",m,E),m.split(/\s+/).map($=>te($,E)).join(" ")),te=(m,E)=>{m=m.trim();const $=E.loose?c[o.XRANGELOOSE]:c[o.XRANGE];return m.replace($,(g,v,R,b,w,S)=>{a("xRange",m,g,v,R,b,w,S);const P=L(R),D=P||L(b),q=D||L(w),re=q;return v==="="&&re&&(v=""),S=E.includePrerelease?"-0":"",P?v===">"||v==="<"?g="<0.0.0-0":g="*":v&&re?(D&&(b=0),w=0,v===">"?(v=">=",D?(R=+R+1,b=0,w=0):(b=+b+1,w=0)):v==="<="&&(v="<",D?R=+R+1:b=+b+1),v==="<"&&(S="-0"),g=`${v+R}.${b}.${w}${S}`):D?g=`>=${R}.0.0${S} <${+R+1}.0.0-0`:q&&(g=`>=${R}.${b}.0${S} <${R}.${+b+1}.0-0`),a("xRange return",g),g})},Ee=(m,E)=>(a("replaceStars",m,E),m.trim().replace(c[o.STAR],"")),me=(m,E)=>(a("replaceGTE0",m,E),m.trim().replace(c[E.includePrerelease?o.GTE0PRE:o.GTE0],"")),ge=m=>(E,$,g,v,R,b,w,S,P,D,q,re)=>(L(g)?$="":L(v)?$=`>=${g}.0.0${m?"-0":""}`:L(R)?$=`>=${g}.${v}.0${m?"-0":""}`:b?$=`>=${$}`:$=`>=${$}${m?"-0":""}`,L(P)?S="":L(D)?S=`<${+P+1}.0.0-0`:L(q)?S=`<${P}.${+D+1}.0-0`:re?S=`<=${P}.${D}.${q}-${re}`:m?S=`<${P}.${D}.${+q+1}-0`:S=`<=${S}`,`${$} ${S}`.trim()),Re=(m,E,$)=>{for(let g=0;g<m.length;g++)if(!m[g].test(E))return!1;if(E.prerelease.length&&!$.includePrerelease){for(let g=0;g<m.length;g++)if(a(m[g].semver),m[g].semver!==u.ANY&&m[g].semver.prerelease.length>0){const v=m[g].semver;if(v.major===E.major&&v.minor===E.minor&&v.patch===E.patch)return!0}return!1}return!0};return ze}var Ye,er;function de(){if(er)return Ye;er=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(r,l){if(l=n(l),r instanceof t){if(r.loose===!!l.loose)return r;r=r.value}r=r.trim().split(/\s+/).join(" "),a("comparator",r,l),this.options=l,this.loose=!!l.loose,this.parse(r),this.semver===e?this.value="":this.value=this.operator+this.semver.version,a("comp",this)}parse(r){const l=this.options.loose?s[i.COMPARATORLOOSE]:s[i.COMPARATOR],h=r.match(l);if(!h)throw new TypeError(`Invalid comparator: ${r}`);this.operator=h[1]!==void 0?h[1]:"",this.operator==="="&&(this.operator=""),h[2]?this.semver=new f(h[2],this.options.loose):this.semver=e}toString(){return this.value}test(r){if(a("Comparator.test",r,this.options.loose),this.semver===e||r===e)return!0;if(typeof r=="string")try{r=new f(r,this.options)}catch{return!1}return u(r,this.operator,this.semver,this.options)}intersects(r,l){if(!(r instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(r.value,l).test(this.value):r.operator===""?r.value===""?!0:new c(this.value,l).test(r.semver):(l=n(l),l.includePrerelease&&(this.value==="<0.0.0-0"||r.value==="<0.0.0-0")||!l.includePrerelease&&(this.value.startsWith("<0.0.0")||r.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&r.operator.startsWith(">")||this.operator.startsWith("<")&&r.operator.startsWith("<")||this.semver.version===r.semver.version&&this.operator.includes("=")&&r.operator.includes("=")||u(this.semver,"<",r.semver,l)&&this.operator.startsWith(">")&&r.operator.startsWith("<")||u(this.semver,">",r.semver,l)&&this.operator.startsWith("<")&&r.operator.startsWith(">")))}}Ye=t;const n=ht(),{safeRe:s,t:i}=ie(),u=$r(),a=fe(),f=G(),c=U();return Ye}var Ke,tr;function pe(){if(tr)return Ke;tr=1;const e=U();return Ke=(n,s,i)=>{try{s=new e(s,i)}catch{return!1}return s.test(n)},Ke}var Ze,rr;function In(){if(rr)return Ze;rr=1;const e=U();return Ze=(n,s)=>new e(n,s).set.map(i=>i.map(u=>u.value).join(" ").trim().split(" ")),Ze}var Je,nr;function $n(){if(nr)return Je;nr=1;const e=G(),t=U();return Je=(s,i,u)=>{let a=null,f=null,c=null;try{c=new t(i,u)}catch{return null}return s.forEach(o=>{c.test(o)&&(!a||f.compare(o)===-1)&&(a=o,f=new e(a,u))}),a},Je}var Qe,sr;function vn(){if(sr)return Qe;sr=1;const e=G(),t=U();return Qe=(s,i,u)=>{let a=null,f=null,c=null;try{c=new t(i,u)}catch{return null}return s.forEach(o=>{c.test(o)&&(!a||f.compare(o)===1)&&(a=o,f=new e(a,u))}),a},Qe}var et,ir;function Tn(){if(ir)return et;ir=1;const e=G(),t=U(),n=he();return et=(i,u)=>{i=new t(i,u);let a=new e("0.0.0");if(i.test(a)||(a=new e("0.0.0-0"),i.test(a)))return a;a=null;for(let f=0;f<i.set.length;++f){const c=i.set[f];let o=null;c.forEach(r=>{const l=new e(r.semver.version);switch(r.operator){case">":l.prerelease.length===0?l.patch++:l.prerelease.push(0),l.raw=l.format();case"":case">=":(!o||n(l,o))&&(o=l);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${r.operator}`)}}),o&&(!a||n(a,o))&&(a=o)}return a&&i.test(a)?a:null},et}var tt,or;function bn(){if(or)return tt;or=1;const e=U();return tt=(n,s)=>{try{return new e(n,s).range||"*"}catch{return null}},tt}var rt,ar;function gt(){if(ar)return rt;ar=1;const e=G(),t=de(),{ANY:n}=t,s=U(),i=pe(),u=he(),a=pt(),f=mt(),c=Et();return rt=(r,l,h,p)=>{r=new e(r,p),l=new s(l,p);let I,d,T,O,N;switch(h){case">":I=u,d=f,T=a,O=">",N=">=";break;case"<":I=a,d=c,T=u,O="<",N="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(i(r,l,p))return!1;for(let L=0;L<l.set.length;++L){const _=l.set[L];let F=null,y=null;if(_.forEach(k=>{k.semver===n&&(k=new t(">=0.0.0")),F=F||k,y=y||k,I(k.semver,F.semver,p)?F=k:T(k.semver,y.semver,p)&&(y=k)}),F.operator===O||F.operator===N||(!y.operator||y.operator===O)&&d(r,y.semver))return!1;if(y.operator===N&&T(r,y.semver))return!1}return!0},rt}var nt,cr;function An(){if(cr)return nt;cr=1;const e=gt();return nt=(n,s,i)=>e(n,s,">",i),nt}var st,ur;function Sn(){if(ur)return st;ur=1;const e=gt();return st=(n,s,i)=>e(n,s,"<",i),st}var it,lr;function On(){if(lr)return it;lr=1;const e=U();return it=(n,s,i)=>(n=new e(n,i),s=new e(s,i),n.intersects(s,i)),it}var ot,fr;function Ln(){if(fr)return ot;fr=1;const e=pe(),t=j();return ot=(n,s,i)=>{const u=[];let a=null,f=null;const c=n.sort((h,p)=>t(h,p,i));for(const h of c)e(h,s,i)?(f=h,a||(a=h)):(f&&u.push([a,f]),f=null,a=null);a&&u.push([a,null]);const o=[];for(const[h,p]of u)h===p?o.push(h):!p&&h===c[0]?o.push("*"):p?h===c[0]?o.push(`<=${p}`):o.push(`${h} - ${p}`):o.push(`>=${h}`);const r=o.join(" || "),l=typeof s.raw=="string"?s.raw:String(s);return r.length<l.length?r:s},ot}var at,hr;function Cn(){if(hr)return at;hr=1;const e=U(),t=de(),{ANY:n}=t,s=pe(),i=j(),u=(l,h,p={})=>{if(l===h)return!0;l=new e(l,p),h=new e(h,p);let I=!1;e:for(const d of l.set){for(const T of h.set){const O=c(d,T,p);if(I=I||O!==null,O)continue e}if(I)return!1}return!0},a=[new t(">=0.0.0-0")],f=[new t(">=0.0.0")],c=(l,h,p)=>{if(l===h)return!0;if(l.length===1&&l[0].semver===n){if(h.length===1&&h[0].semver===n)return!0;p.includePrerelease?l=a:l=f}if(h.length===1&&h[0].semver===n){if(p.includePrerelease)return!0;h=f}const I=new Set;let d,T;for(const A of l)A.operator===">"||A.operator===">="?d=o(d,A,p):A.operator==="<"||A.operator==="<="?T=r(T,A,p):I.add(A.semver);if(I.size>1)return null;let O;if(d&&T){if(O=i(d.semver,T.semver,p),O>0)return null;if(O===0&&(d.operator!==">="||T.operator!=="<="))return null}for(const A of I){if(d&&!s(A,String(d),p)||T&&!s(A,String(T),p))return null;for(const te of h)if(!s(A,String(te),p))return!1;return!0}let N,L,_,F,y=T&&!p.includePrerelease&&T.semver.prerelease.length?T.semver:!1,k=d&&!p.includePrerelease&&d.semver.prerelease.length?d.semver:!1;y&&y.prerelease.length===1&&T.operator==="<"&&y.prerelease[0]===0&&(y=!1);for(const A of h){if(F=F||A.operator===">"||A.operator===">=",_=_||A.operator==="<"||A.operator==="<=",d){if(k&&A.semver.prerelease&&A.semver.prerelease.length&&A.semver.major===k.major&&A.semver.minor===k.minor&&A.semver.patch===k.patch&&(k=!1),A.operator===">"||A.operator===">="){if(N=o(d,A,p),N===A&&N!==d)return!1}else if(d.operator===">="&&!s(d.semver,String(A),p))return!1}if(T){if(y&&A.semver.prerelease&&A.semver.prerelease.length&&A.semver.major===y.major&&A.semver.minor===y.minor&&A.semver.patch===y.patch&&(y=!1),A.operator==="<"||A.operator==="<="){if(L=r(T,A,p),L===A&&L!==T)return!1}else if(T.operator==="<="&&!s(T.semver,String(A),p))return!1}if(!A.operator&&(T||d)&&O!==0)return!1}return!(d&&_&&!T&&O!==0||T&&F&&!d&&O!==0||k||y)},o=(l,h,p)=>{if(!l)return h;const I=i(l.semver,h.semver,p);return I>0?l:I<0||h.operator===">"&&l.operator===">="?h:l},r=(l,h,p)=>{if(!l)return h;const I=i(l.semver,h.semver,p);return I<0?l:I>0||h.operator==="<"&&l.operator==="<="?h:l};return at=u,at}var ct,dr;function wn(){if(dr)return ct;dr=1;const e=ie(),t=le(),n=G(),s=gr(),i=ee(),u=sn(),a=on(),f=an(),c=cn(),o=un(),r=ln(),l=fn(),h=hn(),p=j(),I=dn(),d=pn(),T=dt(),O=En(),N=mn(),L=he(),_=pt(),F=Rr(),y=Ir(),k=Et(),A=mt(),te=$r(),Ee=gn(),me=de(),ge=U(),Re=pe(),m=In(),E=$n(),$=vn(),g=Tn(),v=bn(),R=gt(),b=An(),w=Sn(),S=On(),P=Ln(),D=Cn();return ct={parse:i,valid:u,clean:a,inc:f,diff:c,major:o,minor:r,patch:l,prerelease:h,compare:p,rcompare:I,compareLoose:d,compareBuild:T,sort:O,rsort:N,gt:L,lt:_,eq:F,neq:y,gte:k,lte:A,cmp:te,coerce:Ee,Comparator:me,Range:ge,satisfies:Re,toComparators:m,maxSatisfying:E,minSatisfying:$,minVersion:g,validRange:v,outside:R,gtr:b,ltr:w,intersects:S,simplifyRange:P,subset:D,SemVer:n,re:e.re,src:e.src,tokens:e.t,SEMVER_SPEC_VERSION:t.SEMVER_SPEC_VERSION,RELEASE_TYPES:t.RELEASE_TYPES,compareIdentifiers:s.compareIdentifiers,rcompareIdentifiers:s.rcompareIdentifiers},ct}var vr=wn();const Nn="UNDETERMINED_MACHINE_ID";function Rt(){try{return tn.machineIdSync()}catch{return ue.env.machineId||Nn}}const Pn=Rt(),K=vr.parse(ue.kiroVersion??"0.0.0"),yn=`${K==null?void 0:K.major}.${K==null?void 0:K.minor}`;function Tr(){return{KiroClientVersion:yn,machineId:Pn}}function br(e){return{...Tr(),Activity:e}}function _n(e){return{...Tr(),Tool:e}}let Ar,Sr,Or,Lr,Cr,wr,Nr;function qn(){const e=C.metrics.getMeterProvider().getMeter(Q.Onboarding);Ar=e.createCounter("opened_IDE_count",{description:"Counts the number of times the IDE has been opened",unit:"number"}),Sr=e.createCounter("started_auth_count",{description:"Counts the number of times the IDE has started the auth onboarding flow",unit:"number"}),Or=e.createCounter("failed_auth_count",{description:"Counts the number of times the IDE has failed the onboarding auth flow",unit:"number"}),Lr=e.createCounter("canceled_auth_count",{description:"Counts the number of times a user canceled the onboarding auth flow",unit:"number"}),Cr=e.createCounter("abandoned_auth_count",{description:"Counts the number of times a user abandons the onboarding auth flow (timeout)",unit:"number"}),wr=e.createCounter("finished_auth_count",{description:"Counts the number of times the IDE has finished the onboarding auth flow",unit:"number"}),Nr=e.createCounter("bad_user_input_count",{description:"Counts the number of times bad user input was encountered during onboarding",unit:"number"})}function Pr(e){try{if(!J())return;const n=br("onboarding");switch(e){case"opened-IDE":Ar.add(1,n);break;case"started-login":Sr.add(1,n);break;case"failed-login":Or.add(1,n);break;case"canceled-login":Lr.add(1,n);break;case"abandoned-login":Cr.add(1,n);break;case"finished-login":wr.add(1,n);break;case"bad-user-input":Nr.add(1,n);break}}catch(t){X.logger.error("Failed to record feature latency: ",t)}}let yr,_r,qr,kr,Fr;function kn(){const e=C.metrics.getMeterProvider().getMeter(Q.User);yr=e.createCounter("github_login_count",{description:"Counts the number of times users have logged in with GitHub",unit:"number"}),_r=e.createCounter("google_login_count",{description:"Counts the number of times users have logged in with Google",unit:"number"}),qr=e.createCounter("enterprise_idc_login_count",{description:"Counts the number of times users have logged in with IdC",unit:"number"}),kr=e.createCounter("idc_internal_login_count",{description:"Counts the number of times users have logged in with IdC",unit:"number"}),Fr=e.createCounter("builder_id_login_count",{description:"Counts the number of times users have logged in with Builder ID",unit:"number"})}function Fn(e){try{if(!J())return;const n=br("login");if(e.authMethod=="social")switch(e.provider){case"Google":_r.add(1,n);break;case"Github":yr.add(1,n);break}else if(e.authMethod=="IdC")switch(e.provider){case"BuilderId":Fr.add(1,n);break;case"Enterprise":qr.add(1,n);break;case"Internal":kr.add(1,n);break}}catch(t){X.logger.error("Failed to record auth login metrics: ",t)}}var H=(e=>(e.Application="kiro.application",e.Feature="kiro.feature",e.Continue="kiro.continue",e.Agent="kiro.agent",e.Tool="kiro.tool",e.Parser="kiro.parser",e.Onboarding="kiro.onboarding",e.Webview="kiro.webview",e.Auth="kiro.auth",e))(H||{}),Dr=(e=>(e.Onboarding="onboarding",e))(Dr||{}),se=(e=>(e.RequestId="requestId",e.ConversationId="conversationId",e.ExecutionId="executionId",e.ModelId="ModelIdentifier",e.XRayTraceId="AWS-XRAY-TRACE-ID",e))(se||{});class M{static getContextValue(t){const n=C.propagation.getBaggage(C.context.active());if(!n)return;const s=n.getEntry(t);return s==null?void 0:s.value}static withContextValues(t,n){let i=C.propagation.getBaggage(C.context.active())||C.propagation.createBaggage();for(const[a,f]of Object.entries(t))i=i.setEntry(a,{value:f});const u=C.propagation.setBaggage(C.context.active(),i);return C.context.with(u,()=>{const a=C.trace.getActiveSpan();if(a)for(const[f,c]of Object.entries(t))a.setAttribute(f,c);return n()})}static getTelemetryAttributes(){const t={},n=C.propagation.getBaggage(C.context.active());n&&n.getAllEntries().forEach(([i,u])=>{Object.values(se).includes(i)&&(t[i]=u.value)});const s=C.trace.getActiveSpan();return s&&(t[se.XRayTraceId]=`${s.spanContext().traceId}@${s.spanContext().spanId}`),t}}function ut(e){return function(...t){var i,u,a,f;let n=!1;const s=performance.now();try{(i=e.before)==null||i.call(e);const c=e.callback.apply(this,t);return c instanceof Promise?(n=!0,c.then(o=>{var r;return(r=e.success)==null||r.call(e),o}).catch(o=>{var r;throw(r=e.failure)==null||r.call(e,o),o}).finally(()=>{var o;(o=e.after)==null||o.call(e,performance.now()-s)})):((u=e.success)==null||u.call(e),c)}catch(c){throw(a=e.failure)==null||a.call(e,c),c}finally{n||(f=e.after)==null||f.call(e,performance.now()-s)}}}const Dn=1e3*60*15,W=class W{constructor(t,n){B(this,"namespace");B(this,"scope");this.namespace=t,this.scope=n}extendScope(t){return this.scope?`${this.scope}.${t}`:t}static toNumber(t){return typeof t=="boolean"?t?1:0:t}getOperationTrackers(t,n){const s=this.extendScope(t),i=C.metrics.getMeterProvider().getMeter(this.namespace);return{abort:i.createCounter(`${s}.abort`,{unit:"number"}),abandon:i.createCounter(`${s}.abandon`,{unit:"number"}),badInput:i.createCounter(`${s}.badInput`,{unit:"number"}),unauthorized:i.createCounter(`${s}.unauthorized`,{unit:"number"}),count:i.createCounter(`${s}.count`,{unit:"number"}),failure:i.createCounter(`${s}.failure`,{unit:"number"}),errorType:i.createCounter(`${s}.errorType.${n}`,{unit:"number"}),success:i.createCounter(`${s}.success`,{unit:"number"}),latency:i.createHistogram(`${s}.latency`,{unit:"ms"})}}static async handlePeriodicReporters(){const t=C.metrics.getMeterProvider().getMeter(Q.Periodic);for(const[n,s]of this.periodicReporters){const i=await s();for(const u of Object.keys(i)){const a={...V,Periodic:n},f=`${n}.${u}`,c=this.toNumber(i[u]);c!==void 0&&t.createHistogram(f,{unit:"number"}).record(c,a)}}}static startPeriodicReporterLoop(){this.periodicReporterTimeout&&clearInterval(this.periodicReporterTimeout),this.periodicReporterTimeout=setInterval(()=>void this.handlePeriodicReporters(),Dn)}reportHistogramMetrics(t,n={}){const s=C.metrics.getMeterProvider().getMeter(this.namespace),i={...V,Operation:this.scope,...n,...M.getTelemetryAttributes()};for(const u of Object.keys(t)){const a=this.extendScope(u),f=W.toNumber(t[u]);f!==void 0&&s.createHistogram(a).record(f,i)}}reportCountMetrics(t,n={}){const s=C.metrics.getMeterProvider().getMeter(this.namespace),i={...V,Operation:this.scope,...n,...M.getTelemetryAttributes()};for(const u of Object.keys(t)){const a=this.extendScope(u),f=W.toNumber(t[u]);f!==void 0&&s.createCounter(a).add(f,i)}}wrapMetrics(t,n){const s={...V,Operation:t};return ut({callback:n,before:()=>{const i={...s,...M.getTelemetryAttributes()};this.getOperationTrackers(t).count.add(1,i)},success:()=>{const i={...s,...M.getTelemetryAttributes()};this.getOperationTrackers(t).success.add(1,i)},failure:i=>{const u=X.mapUnknownToErrorType(i),a=this.getOperationTrackers(t,u),f={...s,...M.getTelemetryAttributes()};X.isAbortError(i)?(a.abort.add(1,{...f,errorType:u}),a.failure.add(0,{...f,errorType:u})):(a.abort.add(0,{...f,errorType:u}),a.failure.add(1,{...f,errorType:u})),a.errorType.add(1,{...f,errorType:u}),a.success.add(0,f)},after:i=>{const u={...s,...M.getTelemetryAttributes()};this.getOperationTrackers(t).latency.record(i,u)}})}callWithMetrics(t,n){return this.wrapMetrics(t,n)()}periodicallyCaptureMetrics(t){W.periodicReporters.set(this.scope||"default",t)}forcePeriodicCapture(){W.handlePeriodicReporters()}Metric(t){const n=this.wrapMetrics.bind(this);return function(s,i,u){const a=u.value,f=typeof i=="string"?i:String(i),o=n(t||f,a);u.value=o}}wrapTrace(t,n){const s={...V,Operation:t},i=`${this.scope}.${t}`,u=C.trace.getTracer(H.Application,ne),a=this.getOperationTrackers.bind(this);return function(...f){const c=()=>n.apply(this,f);return u.startActiveSpan(i,o=>ut({callback:c,before:()=>{const l={...s,...M.getTelemetryAttributes()};o.setAttributes(s),a(t).count.add(1,l)},success:()=>{o.setStatus({code:C.SpanStatusCode.OK});const l={...s,...M.getTelemetryAttributes()};a(t).success.add(1,l)},failure:l=>{const h=X.mapUnknownToErrorType(l),p=a(t,h);o.setStatus({code:C.SpanStatusCode.ERROR,message:h}),o.setAttribute("errorType",h);const I={...s,...M.getTelemetryAttributes(),errorType:h};X.isAbortError(l)?(p.abort.add(1,{...I,errorType:h}),p.failure.add(0,{...I,errorType:h})):(p.abort.add(0,{...I,errorType:h}),p.failure.add(1,{...I,errorType:h})),p.errorType.add(1,I),p.success.add(0,I)},after:l=>{const h={...s,...M.getTelemetryAttributes()};a(t).latency.record(l,h),o.end()}})())}}callWithTrace(t,n){return this.wrapTrace(t,n)()}withTrace({traceName:t,metricAliases:n,errorMapper:s},i){const u={...V,Operation:t},a=`${this.scope}.${t}`,f=C.trace.getTracer(H.Application,ne),c=this.getOperationTrackers.bind(this),o=[t,...n||[]],r=l=>i.apply(this,[l]);return f.startActiveSpan(a,l=>ut({callback:()=>r(l),before:()=>{const p={...u,...M.getTelemetryAttributes()};l.setAttributes(u),o.forEach(I=>{const d={...p,Operation:I};c(I).count.add(1,d)})},success:()=>{l.setStatus({code:C.SpanStatusCode.OK});const p={...u,...M.getTelemetryAttributes()};o.forEach(I=>{const d={...p,Operation:I};c(I).success.add(1,d)})},failure:p=>{const I=X.mapUnknownToErrorType(p),d=o.map(N=>({tracker:c(N,I),metricName:N}));l.setStatus({code:C.SpanStatusCode.ERROR,message:I}),l.setAttribute("errorType",I);const T={...u,...M.getTelemetryAttributes(),errorType:I},O=s(p);Object.keys(O).forEach(N=>{d.forEach(({tracker:L,metricName:_})=>{const F={...T,Operation:_,errorType:N};L[N].add(O[N],F)})}),d.forEach(({tracker:N,metricName:L})=>{const _={...T,Operation:L};N.errorType.add(1,_),N.success.add(0,_)})},after:p=>{const I={...u,...M.getTelemetryAttributes()};o.forEach(d=>{const T={...I,Operation:d};c(d).latency.record(p,T)}),l.end()}})())}createTrace(t){const n={...V,Operation:t},s=`${this.scope}.${t}`,i=C.trace.getTracer(H.Application,ne);let u;return{start:()=>{u=i.startSpan(s,{attributes:n})},success:()=>{u&&(u.setStatus({code:C.SpanStatusCode.OK}),u.end())},fail:a=>{if(u){const f=X.mapUnknownToErrorType(a);u.setStatus({code:C.SpanStatusCode.ERROR,message:f}),u.end()}},setAttributes:a=>{u&&u.setAttributes(a)}}}Trace(t){const n=this.wrapTrace.bind(this);return function(s,i,u){const a=u.value,f=typeof i=="string"?i:String(i),o=n(t||f,a);u.value=o}}};B(W,"periodicReporters",new Map),B(W,"periodicReporterTimeout");let z=W;const Mn=Rt(),Gn=`${ce.platform()}-${ce.release()}`,It=ue.kiroVersion??"0.0.0",x=vr.parse(It),Mr=It.split("-").length===1?"stable":"insider",xn=`${x==null?void 0:x.major}.${x==null?void 0:x.minor}`,jn=`${x==null?void 0:x.major}-${x==null?void 0:x.minor}`,pr=new z(H.Application),V={KiroClientVersion:xn,machineId:Mn,platform:Gn,channel:Mr,version:It};function Un(){return pr.reportCountMetrics({osPlatform:1},{Operation:ce.platform()}),pr.reportCountMetrics({kiroChannel:1},{Operation:Mr}),{[`AppPlatform.${ce.platform()}`]:!0,[`AppVersion.${jn}`]:!0}}X.logger.info("Loading telemetry with client attributes",V);const Gr=new z(H.Feature,"features");let ae=new Set;Gr.periodicallyCaptureMetrics(()=>{const e={};for(const t of ae)e[`${t}.usedByUser`]=!0;return ae.size>0&&(e.anyAIFeaturesUsed=!0),ae=new Set,e});function Xn(e){Gr.reportCountMetrics({[`${e}.used`]:!0}),ae.add(e)}const xr=new z(H.Application);xr.periodicallyCaptureMetrics(()=>Un());const Vn={reportUsage:Xn};var jr=(e=>(e.WriteFile="WriteFile",e.GrepSearch="GrepSearch",e.FileSearch="FileSearch",e.ReplaceText="ReplaceText",e.SelfLabel="SelfLabel",e.AppendLines="AppendLines",e.DeleteFile="DeleteFile",e.ExecuteShell="ExecuteShell",e.OpenFolder="OpenFolder",e.FindFiles="FindFiles",e.GetUserInput="GetUserInput",e.ReadFile="ReadFile",e.ReadMultipleFiles="ReadMultipleFiles",e.NativeSearch="NativeSearch",e.MCPWrapper="MCPWrapper",e.TaskStatus="TaskStatus",e))(jr||{});const Hn={WriteFile:{name:"write_file",description:"Tracks write file tool"},GrepSearch:{name:"grep_search",description:"Tracks grep search tool"},FileSearch:{name:"file_search",description:"Tracks file search tool"},ReplaceText:{name:"replace_text",description:"Tracks replace text tool"},SelfLabel:{name:"self_label",description:"Tracks self label tool"},AppendLines:{name:"append_lines",description:"Tracks append lines tool"},DeleteFile:{name:"delete_file",description:"Tracks delete file tool"},ExecuteShell:{name:"execute_shell",description:"Tracks execute shell tool"},OpenFolder:{name:"open_folder",description:"Tracks open folder tool"},FindFiles:{name:"find_files",description:"Tracks find files tool"},GetUserInput:{name:"get_user_input",description:"Tracks get user input tool"},ReadFile:{name:"read_file",description:"Tracks read file tool"},ReadMultipleFiles:{name:"read_multiple_files",description:"Tracks read multiple files tool"},NativeSearch:{name:"native_search",description:"Tracks native search tool"},MCPWrapper:{name:"mcp_wrapper",description:"Tracks any custom MCP tool call"},TaskStatus:{name:"task_status",description:"Tracks the task status tool"}},Z=new z(H.Tool,"tools");let Ur,lt,ft;function Bn(){const e=C.metrics.getMeterProvider().getMeter(Q.Tool);Ur=e.createCounter("count",{description:"tool invoke count",unit:"number"}),lt=e.createCounter("error",{description:"tool error count",unit:"number"}),ft=e.createHistogram("duration",{description:"tool duration",unit:"ms"})}class $t{constructor(t){B(this,"attributes");B(this,"startTime");this.tool=t,this.attributes=_n(Hn[t].name),this.startTime=performance.now(),J()&&Ur.add(1,this.attributes)}static start(t){return Z.reportCountMetrics({toolUsage:1},{Operation:`useTool.${t}`}),Z.reportCountMetrics({[`${t}.invoke`]:1,invoke:1}),new $t(t)}recordToolSuccess(){if(!J())return;lt.add(0,this.attributes),ft.record(performance.now()-this.startTime,this.attributes);const t=performance.now()-this.startTime;Z.reportCountMetrics({[`${this.tool}.success`]:1,[`${this.tool}.failure`]:0,toolSuccess:1,toolFailure:0}),Z.reportHistogramMetrics({[`${this.tool}.latency`]:t,latency:t})}recordToolFailure(t){if(!J())return;lt.add(1,this.attributes),ft.record(performance.now()-this.startTime,this.attributes);const n=performance.now()-this.startTime,s=X.mapUnknownToErrorType(t);Z.reportCountMetrics({[`${this.tool}.success`]:0,[`${this.tool}.failure`]:1,[`${this.tool}.error.${s}`]:1,toolSuccess:0,toolFailure:1,[`toolError.${s}`]:1}),Z.reportHistogramMetrics({[`${this.tool}.latency`]:n,latency:n})}}const Xr="kiroAgent",ne="0.0.1",Er=Rt(),Wn=5e3;let Vr=!1;function J(){return Vr}function Hr(e){C.propagation.setGlobalPropagator(e)}function zn(e){const t=e.endpoint,n=vt.Resource.empty().merge(new vt.Resource({[Tt.ATTR_SERVICE_NAME]:Xr,[Tt.ATTR_SERVICE_VERSION]:ne})),s=new Yr.OTLPTraceExporter({url:t+"/v1/traces",headers:{"x-kiro-machineid":Er}}),i=new zr.OTLPMetricExporter({url:t+"/v1/metrics",temporalityPreference:Y.AggregationTemporality.DELTA,headers:{"x-kiro-machineid":Er}}),u=new Y.PeriodicExportingMetricReader({exporter:i,exportIntervalMillis:Wn}),a=new Y.MeterProvider({resource:n,views:[new Y.View({instrumentType:Y.InstrumentType.HISTOGRAM,aggregation:new Y.ExponentialHistogramAggregation})]});a.addMetricReader(u),C.metrics.setGlobalMeterProvider(a);const f=new Qr.BatchSpanProcessor(s),c=new nn({keys:Object.values(se)}),o=new Kr.AWSXRayIdGenerator,r=new Zr.AWSXRayPropagator,l=new Jr.NodeSDK({textMapPropagator:r,resource:n,spanProcessors:[f,c],traceExporter:s,idGenerator:o,autoDetectResources:!1});return l.start(),Hr(r),qn(),kn(),Bn(),Vr=!0,Pr("opened-IDE"),new ue.Disposable(()=>l.shutdown())}exports.APPLICATION_NAME=Xr;exports.APPLICATION_VERSION=ne;exports.ContextPropagation=M;exports.DefaultAttributes=V;exports.Feature=Vn;exports.JourneyId=Dr;exports.MetricNamespace=Q;exports.MetricReporter=z;exports.Telemetry=xr;exports.TelemetryAttributes=se;exports.TelemetryNamespace=H;exports.Tool=jr;exports.ToolRecorder=$t;exports.initializeBaggagePropagation=Hr;exports.initializeTelemetry=zn;exports.isInitialized=J;exports.recordAuthFromSource=Fn;exports.recordOnboardingStep=Pr;
