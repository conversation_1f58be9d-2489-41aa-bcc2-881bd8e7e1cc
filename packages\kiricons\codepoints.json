{"add": 60000, "plus": 60000, "gist-new": 60000, "repo-create": 60000, "lightbulb": 60001, "light-bulb": 60001, "repo": 60002, "repo-delete": 60002, "gist-fork": 60003, "repo-forked": 60003, "git-pull-request": 60004, "git-pull-request-abandoned": 60004, "record-keys": 60005, "keyboard": 60005, "tag": 60006, "git-pull-request-label": 60006, "tag-add": 60006, "tag-remove": 60006, "person": 60007, "person-follow": 60007, "person-outline": 60007, "person-filled": 60007, "git-branch": 60008, "git-branch-create": 60008, "git-branch-delete": 60008, "source-control": 60008, "mirror": 60009, "mirror-public": 60009, "star": 60010, "star-add": 60010, "star-delete": 60010, "star-empty": 60010, "comment": 60011, "comment-add": 60011, "alert": 60012, "warning": 60012, "search": 60013, "search-save": 60013, "log-out": 60014, "sign-out": 60014, "log-in": 60015, "sign-in": 60015, "eye": 60016, "eye-unwatch": 60016, "eye-watch": 60016, "circle-filled": 60017, "primitive-dot": 60017, "close-dirty": 60017, "debug-breakpoint": 60017, "debug-breakpoint-disabled": 60017, "debug-hint": 60017, "terminal-decoration-success": 60017, "primitive-square": 60018, "edit": 60019, "pencil": 60019, "info": 60020, "issue-opened": 60020, "gist-private": 60021, "git-fork-private": 60021, "lock": 60021, "mirror-private": 60021, "close": 60022, "remove-close": 60022, "x": 60022, "repo-sync": 60023, "sync": 60023, "clone": 60024, "desktop-download": 60024, "beaker": 60025, "microscope": 60025, "vm": 60026, "device-desktop": 60026, "file": 60027, "file-text": 60027, "more": 60028, "ellipsis": 60028, "kebab-horizontal": 60028, "mail-reply": 60029, "reply": 60029, "organization": 60030, "organization-filled": 60030, "organization-outline": 60030, "new-file": 60031, "file-add": 60031, "new-folder": 60032, "file-directory-create": 60032, "trash": 60033, "trashcan": 60033, "history": 60034, "clock": 60034, "folder": 60035, "file-directory": 60035, "symbol-folder": 60035, "logo-github": 60036, "mark-github": 60036, "github": 60036, "terminal": 60037, "console": 60037, "repl": 60037, "zap": 60038, "symbol-event": 60038, "error": 60039, "stop": 60039, "variable": 60040, "symbol-variable": 60040, "array": 60042, "symbol-array": 60042, "symbol-module": 60043, "symbol-package": 60043, "symbol-namespace": 60043, "symbol-object": 60043, "symbol-method": 60044, "symbol-function": 60044, "symbol-constructor": 60044, "symbol-boolean": 60047, "symbol-null": 60047, "symbol-numeric": 60048, "symbol-number": 60048, "symbol-structure": 60049, "symbol-struct": 60049, "symbol-parameter": 60050, "symbol-type-parameter": 60050, "symbol-key": 60051, "symbol-text": 60051, "symbol-reference": 60052, "go-to-file": 60052, "symbol-enum": 60053, "symbol-value": 60053, "symbol-ruler": 60054, "symbol-unit": 60054, "activate-breakpoints": 60055, "archive": 60056, "arrow-both": 60057, "arrow-down": 60058, "arrow-left": 60059, "arrow-right": 60060, "arrow-small-down": 60061, "arrow-small-left": 60062, "arrow-small-right": 60063, "arrow-small-up": 60064, "arrow-up": 60065, "bell": 60066, "bold": 60067, "book": 60068, "bookmark": 60069, "debug-breakpoint-conditional-unverified": 60070, "debug-breakpoint-conditional": 60071, "debug-breakpoint-conditional-disabled": 60071, "debug-breakpoint-data-unverified": 60072, "debug-breakpoint-data": 60073, "debug-breakpoint-data-disabled": 60073, "debug-breakpoint-log-unverified": 60074, "debug-breakpoint-log": 60075, "debug-breakpoint-log-disabled": 60075, "briefcase": 60076, "broadcast": 60077, "browser": 60078, "bug": 60079, "calendar": 60080, "case-sensitive": 60081, "check": 60082, "checklist": 60083, "chevron-down": 60084, "chevron-left": 60085, "chevron-right": 60086, "chevron-up": 60087, "chrome-close": 60088, "chrome-maximize": 60089, "chrome-minimize": 60090, "chrome-restore": 60091, "circle-outline": 60092, "circle": 60092, "debug-breakpoint-unverified": 60092, "terminal-decoration-incomplete": 60092, "circle-slash": 60093, "circuit-board": 60094, "clear-all": 60095, "clippy": 60096, "close-all": 60097, "cloud-download": 60098, "cloud-upload": 60099, "code": 60100, "collapse-all": 60101, "color-mode": 60102, "comment-discussion": 60103, "credit-card": 60105, "dash": 60108, "dashboard": 60109, "database": 60110, "debug-continue": 60111, "debug-disconnect": 60112, "debug-pause": 60113, "debug-restart": 60114, "debug-start": 60115, "debug-step-into": 60116, "debug-step-out": 60117, "debug-step-over": 60118, "debug-stop": 60119, "debug": 60120, "device-camera-video": 60121, "device-camera": 60122, "device-mobile": 60123, "diff-added": 60124, "diff-ignored": 60125, "diff-modified": 60126, "diff-removed": 60127, "diff-renamed": 60128, "diff": 60129, "diff-sidebyside": 60129, "discard": 60130, "editor-layout": 60131, "empty-window": 60132, "exclude": 60133, "extensions": 60134, "eye-closed": 60135, "file-binary": 60136, "file-code": 60137, "file-media": 60138, "file-pdf": 60139, "file-submodule": 60140, "file-symlink-directory": 60141, "file-symlink-file": 60142, "file-zip": 60143, "files": 60144, "filter": 60145, "flame": 60146, "fold-down": 60147, "fold-up": 60148, "fold": 60149, "folder-active": 60150, "folder-opened": 60151, "gear": 60152, "gift": 60153, "gist-secret": 60154, "gist": 60155, "git-commit": 60156, "git-compare": 60157, "compare-changes": 60157, "git-merge": 60158, "github-action": 60159, "github-alt": 60160, "globe": 60161, "grabber": 60162, "graph": 60163, "gripper": 60164, "heart": 60165, "home": 60166, "horizontal-rule": 60167, "hubot": 60168, "inbox": 60169, "issue-reopened": 60171, "issues": 60172, "italic": 60173, "jersey": 60174, "json": 60175, "kebab-vertical": 60176, "key": 60177, "law": 60178, "lightbulb-autofix": 60179, "link-external": 60180, "link": 60181, "list-ordered": 60182, "list-unordered": 60183, "live-share": 60184, "loading": 60185, "location": 60186, "mail-read": 60187, "mail": 60188, "markdown": 60189, "megaphone": 60190, "mention": 60191, "milestone": 60192, "git-pull-request-milestone": 60192, "mortar-board": 60193, "move": 60194, "multiple-windows": 60195, "mute": 60196, "no-newline": 60197, "note": 60198, "octoface": 60199, "open-preview": 60200, "package": 60201, "paintcan": 60202, "pin": 60203, "play": 60204, "run": 60204, "plug": 60205, "preserve-case": 60206, "preview": 60207, "project": 60208, "pulse": 60209, "question": 60210, "quote": 60211, "radio-tower": 60212, "reactions": 60213, "references": 60214, "refresh": 60215, "regex": 60216, "remote-explorer": 60217, "remote": 60218, "remove": 60219, "replace-all": 60220, "replace": 60221, "repo-clone": 60222, "repo-force-push": 60223, "repo-pull": 60224, "repo-push": 60225, "report": 60226, "request-changes": 60227, "rocket": 60228, "root-folder-opened": 60229, "root-folder": 60230, "rss": 60231, "ruby": 60232, "save-all": 60233, "save-as": 60234, "save": 60235, "screen-full": 60236, "screen-normal": 60237, "search-stop": 60238, "server": 60240, "settings-gear": 60241, "settings": 60242, "shield": 60243, "smiley": 60244, "sort-precedence": 60245, "split-horizontal": 60246, "split-vertical": 60247, "squirrel": 60248, "star-full": 60249, "star-half": 60250, "symbol-class": 60251, "symbol-color": 60252, "symbol-constant": 60253, "symbol-enum-member": 60254, "symbol-field": 60255, "symbol-file": 60256, "symbol-interface": 60257, "symbol-keyword": 60258, "symbol-misc": 60259, "symbol-operator": 60260, "symbol-property": 60261, "wrench": 60261, "wrench-subaction": 60261, "symbol-snippet": 60262, "tasklist": 60263, "telescope": 60264, "text-size": 60265, "three-bars": 60266, "thumbsdown": 60267, "thumbsup": 60268, "tools": 60269, "triangle-down": 60270, "triangle-left": 60271, "triangle-right": 60272, "triangle-up": 60273, "twitter": 60274, "unfold": 60275, "unlock": 60276, "unmute": 60277, "unverified": 60278, "verified": 60279, "versions": 60280, "vm-active": 60281, "vm-outline": 60282, "vm-running": 60283, "watch": 60284, "whitespace": 60285, "whole-word": 60286, "window": 60287, "word-wrap": 60288, "zoom-in": 60289, "zoom-out": 60290, "list-filter": 60291, "list-flat": 60292, "list-selection": 60293, "selection": 60293, "list-tree": 60294, "debug-breakpoint-function-unverified": 60295, "debug-breakpoint-function": 60296, "debug-breakpoint-function-disabled": 60296, "debug-stackframe-active": 60297, "circle-small-filled": 60298, "debug-stackframe-dot": 60298, "terminal-decoration-mark": 60298, "debug-stackframe": 60299, "debug-stackframe-focused": 60299, "debug-breakpoint-unsupported": 60300, "symbol-string": 60301, "debug-reverse-continue": 60302, "debug-step-back": 60303, "debug-restart-frame": 60304, "debug-alt": 60305, "call-incoming": 60306, "call-outgoing": 60307, "menu": 60308, "expand-all": 60309, "feedback": 60310, "git-pull-request-reviewer": 60310, "group-by-ref-type": 60311, "ungroup-by-ref-type": 60312, "account": 60313, "git-pull-request-assignee": 60313, "bell-dot": 60314, "debug-console": 60315, "library": 60316, "output": 60317, "run-all": 60318, "sync-ignored": 60319, "pinned": 60320, "github-inverted": 60321, "server-process": 60322, "server-environment": 60323, "pass": 60324, "issue-closed": 60324, "stop-circle": 60325, "play-circle": 60326, "record": 60327, "debug-alt-small": 60328, "vm-connect": 60329, "cloud": 60330, "merge": 60331, "export": 60332, "graph-left": 60333, "magnet": 60334, "notebook": 60335, "redo": 60336, "check-all": 60337, "pinned-dirty": 60338, "pass-filled": 60339, "circle-large-filled": 60340, "circle-large": 60341, "circle-large-outline": 60341, "combine": 60342, "gather": 60342, "table": 60343, "variable-group": 60344, "type-hierarchy": 60345, "type-hierarchy-sub": 60346, "type-hierarchy-super": 60347, "git-pull-request-create": 60348, "run-above": 60349, "run-below": 60350, "notebook-template": 60351, "debug-rerun": 60352, "workspace-trusted": 60353, "workspace-untrusted": 60354, "workspace-unknown": 60355, "terminal-cmd": 60356, "terminal-debian": 60357, "terminal-linux": 60358, "terminal-powershell": 60359, "terminal-tmux": 60360, "terminal-ubuntu": 60361, "terminal-bash": 60362, "arrow-swap": 60363, "copy": 60364, "person-add": 60365, "filter-filled": 60366, "wand": 60367, "debug-line-by-line": 60368, "inspect": 60369, "layers": 60370, "layers-dot": 60371, "layers-active": 60372, "compass": 60373, "compass-dot": 60374, "compass-active": 60375, "azure": 60376, "issue-draft": 60377, "git-pull-request-closed": 60378, "git-pull-request-draft": 60379, "debug-all": 60380, "debug-coverage": 60381, "run-errors": 60382, "folder-library": 60383, "debug-continue-small": 60384, "beaker-stop": 60385, "graph-line": 60386, "graph-scatter": 60387, "pie-chart": 60388, "bracket": 60175, "bracket-dot": 60389, "bracket-error": 60390, "lock-small": 60391, "azure-devops": 60392, "verified-filled": 60393, "newline": 60394, "layout": 60395, "layout-activitybar-left": 60396, "layout-activitybar-right": 60397, "layout-panel-left": 60398, "layout-panel-center": 60399, "layout-panel-justify": 60400, "layout-panel-right": 60401, "layout-panel": 60402, "layout-sidebar-left": 60403, "layout-sidebar-right": 60404, "layout-statusbar": 60405, "layout-menubar": 60406, "layout-centered": 60407, "target": 60408, "indent": 60409, "record-small": 60410, "error-small": 60411, "terminal-decoration-error": 60411, "arrow-circle-down": 60412, "arrow-circle-left": 60413, "arrow-circle-right": 60414, "arrow-circle-up": 60415, "layout-sidebar-right-off": 60416, "layout-panel-off": 60417, "layout-sidebar-left-off": 60418, "blank": 60419, "heart-filled": 60420, "map": 60421, "map-horizontal": 60421, "fold-horizontal": 60421, "map-filled": 60422, "map-horizontal-filled": 60422, "fold-horizontal-filled": 60422, "circle-small": 60423, "bell-slash": 60424, "bell-slash-dot": 60425, "comment-unresolved": 60426, "git-pull-request-go-to-changes": 60427, "git-pull-request-new-changes": 60428, "search-fuzzy": 60429, "comment-draft": 60430, "send": 60431, "sparkle": 60432, "insert": 60433, "mic": 60434, "thumbsdown-filled": 60435, "thumbsup-filled": 60436, "coffee": 60437, "snake": 60438, "game": 60439, "vr": 60440, "chip": 60441, "piano": 60442, "music": 60443, "mic-filled": 60444, "repo-fetch": 60445, "copilot": 60446, "lightbulb-sparkle": 60447, "robot": 60448, "sparkle-filled": 60449, "diff-single": 60450, "diff-multiple": 60451, "surround-with": 60452, "share": 60453, "git-stash": 60454, "git-stash-apply": 60455, "git-stash-pop": 60456, "vscode": 60457, "vscode-insiders": 60458, "code-oss": 60459, "run-coverage": 60460, "run-all-coverage": 60461, "coverage": 60462, "github-project": 60463, "map-vertical": 60464, "fold-vertical": 60464, "map-vertical-filled": 60465, "fold-vertical-filled": 60465, "go-to-search": 60466, "percentage": 60467, "sort-percentage": 60467, "attach": 60468, "go-to-editing-session": 60469, "edit-session": 60470, "code-review": 60471, "copilot-warning": 60472, "python": 60473, "copilot-large": 60474, "copilot-warning-large": 60475, "keyboard-tab": 60476, "copilot-blocked": 60477, "copilot-not-connected": 60478, "flag": 60479, "lightbulb-empty": 60480, "symbol-method-arrow": 60481, "copilot-unavailable": 60482, "repo-pinned": 60483, "keyboard-tab-above": 60484, "keyboard-tab-below": 60485, "git-fetch": 61697, "hook": 61698, "shrink": 61699, "restore": 61700, "reorder": 61701, "rejected": 61702, "pending": 61703, "pending-copy": 61704, "new-space": 61705, "info-small": 61706, "in-progress": 61707, "image": 61708, "complete": 61709, "cmd": 61710, "clipboard": 61711, "cancel-queue": 61712, "cancel-queue-copy": 61713, "call": 61714, "brush": 61715, "kiro": 61716}