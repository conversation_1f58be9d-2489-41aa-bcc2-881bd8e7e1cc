{"KiroAgent.title": "<PERSON><PERSON>", "KiroAgent.debugTitle": "<PERSON><PERSON>", "KiroAgent.command.generateTest": "$(add) Requirement (⌘Enter)", "KiroAgent.debug.openTestNotebook": "Open Test Notebook", "KiroAgent.debug.resetOnboardingState": "Reset On-Boarding State", "KiroAgent.debug.openMetadata": "Open Metadata Storage Path", "KiroAgent.debug.purgeMetadata": "Purge Metadata Storage Path", "KiroAgent.debug.setQOnboardingState": "Show Q Onboarding page", "KiroAgent.testSpecificationEditor.title": "Test Specification Editor", "KiroAgent.chatContext.title": "Chat Context", "KiroAgent.agent.askAgent": "Ask Agent", "KiroAgent.command.captureLog": "Capture logs", "KiroAgent.command.captureLLMLog": "Capture LLM logs", "KiroAgent.command.installOllama": "Install Ollama", "KiroAgent.views.hooks": "Agent <PERSON>", "KiroAgent.views.steering": "Agent St<PERSON>", "KiroAgent.cancelRunningAgent": "Cancel Agent", "KiroAgent.clearOutput": "Clear Agent Execution Log", "KiroAgent.agent.promptAgent": "Prompt Agent", "KiroAgent.hooks.openHooksEditor": "Open Hook Editor", "KiroAgent.agent.rewriteRange": "Rewrite Selected Range", "KiroAgent.checkpoints.createCheckpoint": "Create Checkpoint", "KiroAgent.checkpoints.revertToLastCheckpoint": "Revert to Last Checkpoint", "KiroAgent.checkpoints.acceptDiff": "Accept Block", "KiroAgent.checkpoints.restoreDiff": "<PERSON><PERSON>", "KiroAgent.command.recordReferences": "Record References", "KiroAgent.command.edit.instructions": "What edits would you like to make? (# to add context, ⇅ for history)", "KiroAgent.command.enableShellIntegration": "Enable Shell Integration", "KiroAgent.command.execution.viewAllChanges": "View Execution Changes", "KiroAgent.command.inlineChat.start": "Edit", "KiroAgent.command.acceptDiff": "Accept <PERSON><PERSON>", "KiroAgent.command.rejectDiff": "Reject Diff", "KiroAgent.command.acceptVerticalDiffBlock": "Accept Vertical Diff Block", "KiroAgent.command.rejectVerticalDiffBlock": "Reject Vertical Diff Block", "KiroAgent.command.quickEdit": "Generate Code", "KiroAgent.command.focusContinueInput": "Cha<PERSON>", "KiroAgent.command.debugTerminal": "Debug Terminal", "KiroAgent.command.toggleTabAutocompleteEnabled": "Toggle Autocomplete Enabled", "KiroAgent.command.selectFilesAsContext": "Select Files as Context", "KiroAgent.command.newSession": "New Session", "KiroAgent.command.viewHistory": "View History", "KiroAgent.command.writeCommentsForCode": "Write Comments for this Code", "KiroAgent.command.writeDocstringForCode": "Write a Docstring for this Code", "KiroAgent.command.fixCode": "Fix this Code", "KiroAgent.command.optimizeCode": "Optimize this Code", "KiroAgent.command.fixGrammar": "Fix Grammar / Spelling", "KiroAgent.command.codebaseForceReIndex": "Codebase Force Re-Index", "KiroAgent.command.rebuildCodebaseIndex": "Rebuild codebase index", "KiroAgent.command.docsIndex": "Docs Index", "KiroAgent.command.docsReIndex": "Docs Force Re-Index", "KiroAgent.views.mcpServerStatus": "MCP Servers", "KiroAgent.command.openActiveMcpConfig": "Open MCP Config", "KiroAgent.command.openWorkspaceMcpConfig": "Open Workspace MCP Config (JSON)", "KiroAgent.command.openWorkspaceMcpConfig.short": "Workspace Config", "KiroAgent.command.openUserMcpConfig": "Open User MCP Config (JSON)", "KiroAgent.command.openUserMcpConfig.short": "User Config", "KiroAgent.editor.spec.title": "Rich Spec Editor", "KiroAgent.editor.hook.title": "<PERSON>", "KiroAgent.notebook.spec.title": "Spec View", "KiroAgent.command.debug.generateContextualDiscoveredSpec": "Generate Contextual Spec", "KiroAgent.hookContext.kiroHook": "<PERSON><PERSON>", "KiroAgent.hooks.openUI": "Open Kiro Hook UI", "KiroAgent.executions.uiControl": "Control the execution log UI", "Kiro.steering.createInitialSteering": "Generate project steering documents", "Kiro.steering.createSteering": "Generate a custom steering document", "Kiro.steering.refineSteeringFile": "Refine this Steering document", "Kiro.spec.navigateToRequirements": "Requirements", "Kiro.spec.navigateToDesign": "Design", "Kiro.spec.navigateToTasks": "Task list", "Kiro.spec.navigateToRequirements.long": "Go to requirements", "Kiro.spec.navigateToDesign.long": "Go to design", "Kiro.spec.navigateToTasks.long": "Go to task list", "Kiro.spec.refreshRequirementsFile": "Refine", "Kiro.spec.refreshDesignFile": "Refine", "Kiro.spec.refreshPlanFile": "Refine", "Kiro.spec.refreshRequirementsFile.long": "Regenerate the requirements in EARS format", "Kiro.spec.refreshDesignFile.long": "Refresh your design based on the requirements", "Kiro.spec.refreshPlanFile.long": "Refresh task list after code changes or spec updates", "Kiro.spec.explorerCreateSpec": "Create a new spec", "KiroAgent.treeview.welcome.mcp": "Connect external tools and data sources", "KiroAgent.treeview.welcome.mcp.disabled": "Connect external tools and data sources \n\n[Enable MCP](command:kiroAgent.mcp.enable)"}