"use strict";var D=Object.defineProperty;var _=(o,e,r)=>e in o?D(o,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[e]=r;var s=(o,e,r)=>_(o,typeof e!="symbol"?e+"":e,r);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const t=require("./sso-oidc-client-DPYFFpGi.cjs"),L=require("vscode"),h=require("./errors-CP8CRW6V.cjs"),b=require("path"),N=require("os"),O=require("fs");require("node-machine-id");const C=require("./social-auth-provider-K_M4Kprz.cjs"),i=require("./initialize-Ci0T2sI9.cjs"),f=require("./span-D2eRFwI2.cjs");require("@opentelemetry/api");const R=require("child_process"),F=require("util"),g=require("./config-B9h4yehs.cjs"),p=require("./journey-tracker-D7JvbnBj.cjs"),m=require("./paths-CcTkS6I0.cjs"),S=require("./machine-id-DsgqNrtt.cjs");function T(o){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(o){for(const r in o)if(r!=="default"){const n=Object.getOwnPropertyDescriptor(o,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:()=>o[r]})}}return e.default=o,Object.freeze(e)}const a=T(L),P=T(b),x=T(N),u=T(O),U="kiro-auth-token.json";class q{constructor(){s(this,"tokenCache");s(this,"cacheDirectory",P.join(x.homedir(),".aws","sso","cache"));s(this,"_onDidChange",new a.EventEmitter);s(this,"watchListener");this.watchListener=()=>{this.clearCache(),this._onDidChange.fire()},u.watchFile(this.getAuthTokenPath(),this.watchListener)}dispose(){u.unwatchFile(this.getAuthTokenPath(),this.watchListener)}get onDidChange(){return this._onDidChange.event}readTokenFromLocalCache(){return this.tokenCache}getAuthTokenPath(){return P.join(this.cacheDirectory,U)}ensureCacheDirectory(){u.existsSync(this.cacheDirectory)||u.mkdirSync(this.cacheDirectory,{recursive:!0})}writeTokenToLocalCache(e){this.tokenCache=e}writeTokenToDisk(e){this.ensureCacheDirectory(),u.writeFileSync(this.getAuthTokenPath(),JSON.stringify(e,void 0,2))}clearCache(){this.tokenCache=void 0}readTokenFromDisk(){const e=this.getAuthTokenPath();if(u.existsSync(e)){const r=u.readFileSync(e,"utf8");try{return JSON.parse(r)}catch(n){h.logger.error("Error trying to parse the token file.",n);return}}}readToken(){const e=this.readTokenFromLocalCache();if(e)return e;const r=this.readTokenFromDisk();if(r)return this.writeTokenToLocalCache(r),r}writeToken(e){this.writeTokenToDisk(e),this.clearCache(),this._onDidChange.fire()}clearToken(){this.clearCache(),u.unlinkSync(this.getAuthTokenPath()),this._onDidChange.fire()}}const y=3*60,H=10*60,W=60,w=new i.MetricReporter(i.TelemetryNamespace.Auth,"auth-provider"),B=o=>o instanceof t.CanceledError?{abort:1}:o instanceof t.AbandonedError?{abandon:1}:o instanceof t.InvalidUserInputError?{badInput:1}:t.isBadAuthIssue(o)?{unauthorized:1}:{failure:1};function E(o){return o instanceof t.AuthError?o:new t.UnexpectedIssueError("Auth provider: unexpected issue")}function J(o){return o.toString()||"unknown"}function k(o,e){return{traceName:`${J(e)}.${o}`,errorMapper:B,metricAliases:[o]}}class M{constructor(){s(this,"storage");s(this,"signInDeferred");s(this,"signInPromise");s(this,"providers");s(this,"authErrorMessagePromises",{AccessDenied:null,NetworkIssue:null,Unknown:null});s(this,"refreshSettled",Promise.resolve());s(this,"refreshLoopTimeout");s(this,"_onDidChangeLoginStatus",new a.EventEmitter);s(this,"_onDidPerformUserInitiatedLogout",new a.EventEmitter);s(this,"disposables",[]);this.storage=new q,this.providers={IdC:new C.IDCAuthProvider,social:new C.SocialAuthProvider},a.window.state.focused&&this.startRefreshLoop(),this.disposables.push(this.storage,{dispose:()=>{this.stopRefreshLoop()}},this.storage.onDidChange(()=>{this.handleTokenChanges()}),a.window.onDidChangeWindowState(e=>{e.focused?this.startRefreshLoop():this.stopRefreshLoop()}))}get onDidChangeLoginStatus(){return this._onDidChangeLoginStatus.event}get onDidPerformUserInitiatedLogout(){return this._onDidPerformUserInitiatedLogout.event}dispose(){this.disposables.forEach(e=>{e.dispose()})}stopRefreshLoop(){this.refreshLoopTimeout&&clearInterval(this.refreshLoopTimeout)}startRefreshLoop(){this.stopRefreshLoop(),this.refreshSettled=this.attemptRefreshIfCloseToExpiry(),this.refreshLoopTimeout=setInterval(()=>{this.refreshSettled=this.attemptRefreshIfCloseToExpiry()},W*1e3)}handleTokenChanges(){if(this.isLoggedIn()){const e=this.storage.readToken();this.signInDeferred&&this.signInDeferred.resolve(e),this._onDidChangeLoginStatus.fire({isSignedIn:!0,token:e})}else this._onDidChangeLoginStatus.fire({isSignedIn:!1,token:void 0})}async attemptRefreshIfCloseToExpiry(){try{const e=this.storage.readToken();if(!e||!e.expiresAt||!e.accessToken)return;this.isAuthTokenExpiredWithinSeconds(e,H)&&await f.withSpan(i.TelemetryNamespace.Auth,"auth-provider.scheduled-refresh",()=>this.refreshToken())}catch(e){const r=this.storage.readToken();t.isBadAuthIssue(e)&&r&&this.isAuthTokenExpiredWithinSeconds(r,y)&&this.logoutAndForget()}}isAuthTokenExpiredWithinSeconds(e,r){if(!e.expiresAt||!e.accessToken)return!0;const n=new Date(e.expiresAt),c=new Date;return n.valueOf()<c.valueOf()+r*1e3}isAuthTokenExpired(e){return this.isAuthTokenExpiredWithinSeconds(e,y)}async getToken(){return(await this.getTokenData()).accessToken}readToken(){return this.storage.readToken()}async getProfileArn(){const e=await this.getTokenData();return"profileArn"in e?e.profileArn:void 0}async getTokenData({attemptRefresh:e}={attemptRefresh:!0}){await this.refreshSettled;try{const r=this.storage.readToken();if(!r)throw new t.MissingTokenError("No valid token found");if(!this.isAuthTokenExpired(r))return r;if(r.refreshToken&&e)return await f.withSpan(i.TelemetryNamespace.Auth,"auth-provider.getTokenData",async()=>(await this.refreshToken(),await this.getTokenData({attemptRefresh:!1})));throw new t.MalformedTokenError("No valid token found")}catch(r){throw t.isBadAuthIssue(r)&&this.logoutAndForget(),h.logger.error("Failed to retrieve auth token:",r),E(r)}}isLoggedIn(){const e=this.storage.readToken();return!!(e!=null&&e.refreshToken)}async logout(){if(!this.isLoggedIn())return;const e=this.storage.readToken();if(e)return w.withTrace(k("logout",e.provider),async r=>{r.setAttribute("authProvider",e.provider);const n=this.providers[e.authMethod];this.storage.clearToken();try{await n.logout(e)}catch(c){throw h.logger.error("Failed to logout:",c),E(c)}})}async logoutAndForget(){try{await this.logout()}catch{}}async deleteAccount(){if(!this.isLoggedIn())throw new t.InvalidAuthError("Not logged in");const e=this.storage.readToken();if(!e)throw new t.MissingTokenError("No token available");return w.withTrace(k("deleteAccount",e.provider),async r=>{r.setAttribute("authProvider",e.provider);const n=this.providers[e.authMethod];try{await n.deleteAccount(e),this.storage.clearToken(),this._onDidPerformUserInitiatedLogout.fire()}catch(c){throw h.logger.error("Failed to delete account:",c),E(c)}})}async refreshToken(){const e=this.storage.readToken();if(!(e!=null&&e.refreshToken))throw new t.InvalidAuthError("No valid refresh token found");return w.withTrace(k("refreshToken",e.provider),async r=>{var n;r.setAttribute("authProvider",e.provider);try{const v=await this.providers[e.authMethod].refreshToken(e);((n=this.storage.readToken())==null?void 0:n.refreshToken)===e.refreshToken&&this.storage.writeToken(v)}catch(c){throw h.logger.error("Failed to refresh token:",c),c}})}async openInternalLink(e){const r=await a.env.asExternalUri(a.Uri.parse(`${a.env.uriScheme}://kiro.kiroAgent${e}`));await a.env.openExternal(r)}async authenticateWithOptions(e){return i.recordOnboardingStep("started-login"),w.withTrace(k("authenticate",e.provider),async r=>{r.setAttribute("authProvider",e.provider),this.isLoggedIn()&&await this.logout();try{const c=await this.providers[e.authMethod].authenticate(e);this.storage.writeToken(c),await this.openInternalLink("/did-authenticate"),i.recordOnboardingStep("finished-login"),i.recordAuthFromSource(e)}catch(n){throw n instanceof t.CanceledError?(h.logger.info("Authentication canceled"),i.recordOnboardingStep("canceled-login")):n instanceof t.AbandonedError?(h.logger.info("Authentication timed out"),i.recordOnboardingStep("abandoned-login")):n instanceof t.InvalidUserInputError?(h.logger.error("Authentication failed due to bad user input:",n),i.recordOnboardingStep("bad-user-input")):(h.logger.error("Authentication failed:",n),i.recordOnboardingStep("failed-login"),a.window.showErrorMessage("Failed to authenticate with Kiro.")),await this.logout(),n}})}cancelSignIn(){this.providers.IdC.cancelAuth(),this.providers.social.cancelAuth()}async handleAuthError(e){return e instanceof t.AccessDeniedError||e instanceof t.MissingTokenError||e instanceof t.MalformedTokenError||e instanceof t.InvalidAuthError||e instanceof t.InvalidSSOAuthError||e instanceof t.InvalidIdCAuthError?this.showInvalidSessionErrorMessage():e instanceof t.NetworkIssueError?this.showNetworkIssueErrorMessage():this.showUnknownIssueErrorMessage()}async showInvalidSessionErrorMessage(){return f.withSpan(i.TelemetryNamespace.Auth,"auth-provider.manual-error-resolve",async()=>{if(this.authErrorMessagePromises.AccessDenied)return this.authErrorMessagePromises.AccessDenied;const e=a.window.showErrorMessage("Could not complete the request because your session is invalid or expired.","Refresh session","Login");this.authErrorMessagePromises.AccessDenied=e;let r=await e;if(r==="Refresh session")try{await this.refreshToken(),a.window.showInformationMessage("Your session was successfully refreshed.")}catch{this.logoutAndForget(),r=await a.window.showErrorMessage("We are unable to refresh your session.","Login")}r==="Login"&&(await this.logout(),this._onDidPerformUserInitiatedLogout.fire()),this.authErrorMessagePromises.AccessDenied=null})}async showNetworkIssueErrorMessage(){if(this.authErrorMessagePromises.NetworkIssue)return this.authErrorMessagePromises.NetworkIssue;const e=a.window.showErrorMessage("Could not communicate with the service. Please check your network connection.","Dismiss");this.authErrorMessagePromises.NetworkIssue=e,await e,this.authErrorMessagePromises.NetworkIssue=null}async showUnknownIssueErrorMessage(){if(this.authErrorMessagePromises.Unknown)return this.authErrorMessagePromises.Unknown;const e=a.window.showErrorMessage("An unexpected issue occurred.","Dismiss");this.authErrorMessagePromises.Unknown=e,await e,this.authErrorMessagePromises.Unknown=null}async waitForSignIn(){return this.isLoggedIn()?this.storage.readToken():(this.signInPromise||(this.signInPromise=new Promise(e=>{this.signInDeferred={resolve:e}}).then(e=>(this.signInPromise=void 0,this.signInDeferred=void 0,e))),this.signInPromise)}}const j=F.promisify(R.exec),z="mwinit",K=process.platform=="win32"?"where.exe":"which";async function $(){try{return await j(`${K} ${z}`),!0}catch{return!1}}const d=new M,I="kiro",V={BuilderId:"BuilderId",Enterprise:"AWS IAM Identity Center",Internal:"Amazon internal (Midway)",Github:"GitHub",Google:"Google"};class l{constructor(e){s(this,"account");s(this,"id",I);s(this,"scopes",[]);s(this,"accessToken","");this.account={id:I,label:e?V[e]:""}}}class A{constructor(e){s(this,"disposables",[]);s(this,"_onDidChangeSessions",new a.EventEmitter);this.controller=e,this.disposables.push(d.onDidChangeLoginStatus(({token:r,isSignedIn:n})=>{n?this._onDidChangeSessions.fire({removed:[],added:[new l(r.provider)],changed:[]}):this._onDidChangeSessions.fire({removed:[new l],added:[],changed:[]})}),d.onDidPerformUserInitiatedLogout(()=>{this.controller.showSignInPage()}))}get onDidChangeSessions(){return this._onDidChangeSessions.event}dispose(){this.disposables.forEach(e=>{e.dispose()})}async getSessions(e,r){if(d.isLoggedIn()){const n=d.readToken();return Promise.resolve([new l(n.provider)])}return Promise.resolve([])}async createSession(e,r){if(d.isLoggedIn()){const c=d.readToken();return new l(c.provider)}this.controller.showSignInPage();const n=await d.waitForSignIn();return new l(n.provider)}async removeSession(e){d.isLoggedIn()&&(await d.logout(),this.controller.showSignInPage())}}s(A,"name","Kiro");class G{constructor(){s(this,"_onDidReceiveSignInRequest",new a.EventEmitter)}get onDidReceiveSignInRequest(){return this._onDidReceiveSignInRequest.event}showSignInPage(){this._onDidReceiveSignInRequest.fire()}signIn(e){return d.authenticateWithOptions(e)}cancelSignIn(){d.cancelSignIn()}}async function Y(o){const e=new G,r=new A(e),n=a.authentication.registerAuthenticationProvider(I,A.name,r),c=await $(),v=a.authentication.registerSignInController(I,e,{isInternalUser:c});o.subscriptions.push(r,n,v,d)}exports.AbandonedError=t.AbandonedError;exports.AccessDeniedError=t.AccessDeniedError;exports.AuthError=t.AuthError;exports.AuthErrorType=t.AuthErrorType;exports.CanceledError=t.CanceledError;exports.InvalidAuthError=t.InvalidAuthError;exports.InvalidIdCAuthError=t.InvalidIdCAuthError;exports.InvalidSSOAuthError=t.InvalidSSOAuthError;exports.InvalidUserInputError=t.InvalidUserInputError;exports.MalformedTokenError=t.MalformedTokenError;exports.MissingTokenError=t.MissingTokenError;exports.NetworkIssueError=t.NetworkIssueError;exports.ServerIssueError=t.ServerIssueError;exports.UnexpectedIssueError=t.UnexpectedIssueError;exports.isBadAuthIssue=t.isBadAuthIssue;exports.TrustedError=h.TrustedError;exports.isAbortError=h.isAbortError;exports.logger=h.logger;exports.mapUnknownToErrorType=h.mapUnknownToErrorType;exports.mcpLogger=h.mcpLogger;exports.APPLICATION_NAME=i.APPLICATION_NAME;exports.APPLICATION_VERSION=i.APPLICATION_VERSION;exports.ContextPropagation=i.ContextPropagation;exports.Feature=i.Feature;exports.JourneyId=i.JourneyId;exports.MetricNamespace=i.MetricNamespace;exports.MetricReporter=i.MetricReporter;exports.Telemetry=i.Telemetry;exports.TelemetryAttributes=i.TelemetryAttributes;exports.TelemetryNamespace=i.TelemetryNamespace;exports.Tool=i.Tool;exports.ToolRecorder=i.ToolRecorder;exports.initializeBaggagePropagation=i.initializeBaggagePropagation;exports.initializeTelemetry=i.initializeTelemetry;exports.isInitialized=i.isInitialized;exports.recordAuthFromSource=i.recordAuthFromSource;exports.recordOnboardingStep=i.recordOnboardingStep;exports.startActiveSpan=f.startActiveSpan;exports.withSpan=f.withSpan;exports.MCPConnection=g.MCPConnection;exports.MCPJsonConfigSchema=g.MCPJsonConfigSchema;exports.MCPManagerSingleton=g.MCPManagerSingleton;exports.MCPOptionsSchema=g.MCPOptionsSchema;exports.addMCPToolToAutoApproveConfig=g.addMCPToolToAutoApproveConfig;exports.findConfigFileForServer=g.findConfigFileForServer;exports.formatToolName=g.formatToolName;exports.loadMcpConfig=g.loadMcpConfig;exports.JourneyTracker=p.JourneyTracker;exports.Metrics=p.Metrics;exports.createCounter=p.createCounter;exports.createHistogram=p.createHistogram;exports.getJourneyTracker=p.getJourneyTracker;exports.getActiveMcpConfigLocation=m.getActiveMcpConfigLocation;exports.getHomeKiroPath=m.getHomeKiroPath;exports.getWorkspaceKiroPath=m.getWorkspaceKiroPath;exports.addPrivacyHeadersMiddleware=S.addPrivacyHeadersMiddleware;exports.getMachineId=S.getMachineId;exports.updateResolvedIDESetting=S.updateResolvedIDESetting;exports.AuthProvider=M;exports.authProvider=d;exports.registerAuthProviderExtension=Y;
