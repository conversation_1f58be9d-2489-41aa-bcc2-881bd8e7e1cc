"use strict";const c=require("vscode"),s=require("./errors-CP8CRW6V.cjs"),u=require("node-machine-id");function l(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const r in e)if(r!=="default"){const n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:()=>e[r]})}}return t.default=e,Object.freeze(t)}const d=l(c);function p(e,t="CodeWhisperer"){if(!d.env.isTelemetryEnabled){s.logger.debug(`${t}: telemetry is disabled, setting x-amzn-codewhisperer-opt-out to true`);const r=a=>async i=>{const o=i;return o.request.headers={...o.request.headers,"x-amzn-codewhisperer-opt-out":"true"},a(i)};e.middlewareStack.add(r,{step:"build"})}}async function g(e,t,r,n){var i;const a=d.workspace.getConfiguration(e,n);((i=a.inspect(t))==null?void 0:i.workspaceValue)!==void 0?await a.update(t,r,d.ConfigurationTarget.Workspace):await a.update(t,r,d.ConfigurationTarget.Global)}const f="UNDETERMINED_MACHINE_ID";function h(){try{return u.machineIdSync()}catch{return d.env.machineId||f}}exports.addPrivacyHeadersMiddleware=p;exports.getMachineId=h;exports.updateResolvedIDESetting=g;
