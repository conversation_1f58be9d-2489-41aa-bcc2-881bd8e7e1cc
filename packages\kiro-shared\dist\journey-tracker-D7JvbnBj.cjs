"use strict";var l=Object.defineProperty;var f=(r,e,t)=>e in r?l(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var o=(r,e,t)=>f(r,typeof e!="symbol"?e+"":e,t);const p=require("@opentelemetry/api"),h=require("./initialize-Ci0T2sI9.cjs");class m{constructor(e){o(this,"counters",new Map);o(this,"histograms",new Map);o(this,"namespace");o(this,"meter");this.namespace=e,this.meter=d(e)}addCount(e,t,s={}){const n=this.counters.get(e)||[];n.push({value:t,attributes:s}),this.counters.set(e,n)}addHistogram(e,t,s={}){const n=this.histograms.get(e)||[];n.push({value:t,attributes:s}),this.histograms.set(e,n)}flush(){for(const[e,t]of this.counters.entries()){const s=this.meter.createCounter(e);for(const{value:n,attributes:i}of t)s.add(n,i)}this.counters.clear();for(const[e,t]of this.histograms.entries()){const s=this.meter.createHistogram(e);for(const{value:n,attributes:i}of t)s.record(n,i)}this.histograms.clear()}}function u(r,e,t){return d(r).createCounter(e,t)}function y(r,e,t){return d(r).createHistogram(e,t)}const a={};function d(r){if(r in a)return a[r];const e=p.metrics.getMeter(r,h.APPLICATION_VERSION);return h.isInitialized()&&(a[r]=e),e}const c={};function g(r){if(r in c)return c[r];const e=new J(r);return c[r]=e,e}class J{constructor(e){o(this,"namespace");o(this,"activeJourneys",new Map);this.namespace=e}startJourney(e){const t=this.activeJourneys.get(e.id);if(t)return t.uniqueId;const s=`${e.id}-${Date.now()}-${Math.random().toString(36).slice(2,8)}`,n=setTimeout(()=>{this.timeoutJourney(e.id)},e.timeoutMs),i={journeyId:e.id,uniqueId:s,timeoutId:n,startTime:performance.now(),durationTracker:y(this.namespace,`journey.${e.id}.duration`,{unit:"milliseconds"}),metrics:new m(this.namespace),onJourneyEnd:e.onJourneyEnd};return this.activeJourneys.set(e.id,i),u(this.namespace,`journey.${e.id}.started`).add(1),s}completeJourney(e,t={}){this.activeJourneys.get(e)&&(this.cleanupJourney(e),u(this.namespace,`journey.${e}.completed`).add(1,{...t}))}timeoutJourney(e){this.activeJourneys.get(e)&&(this.cleanupJourney(e),u(this.namespace,`journey.${e}.time_out`).add(1,{}))}cleanupJourney(e){const t=this.activeJourneys.get(e);t&&(t.onJourneyEnd&&t.onJourneyEnd(t.metrics),t.durationTracker.record(performance.now()-t.startTime),t.metrics.flush(),clearTimeout(t.timeoutId),this.activeJourneys.delete(e))}dispose(){for(const[e,t]of this.activeJourneys)this.cleanupJourney(e),u(this.namespace,`journey.${e}.disposed`).add(1,{journeyId:e,uniqueJourneyId:t.uniqueId});this.activeJourneys.clear()}}exports.JourneyTracker=J;exports.Metrics=m;exports.createCounter=u;exports.createHistogram=y;exports.getJourneyTracker=g;
