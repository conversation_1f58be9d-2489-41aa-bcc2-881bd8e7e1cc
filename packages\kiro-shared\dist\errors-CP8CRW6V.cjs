"use strict";const S=require("vscode");function E(o){const r=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(o){for(const t in o)if(t!=="default"){const n=Object.getOwnPropertyDescriptor(o,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:()=>o[t]})}}return r.default=o,Object.freeze(r)}const d=E(S),i=d.window.createOutputChannel("Kiro Logs",{log:!0}),f=[];function s(o,r,...t){const n=t.map(e=>typeof e=="object"?JSON.stringify(e):e).join(" "),u=`[${new Date().toISOString()}] [${o}] ${r} ${n}`;f.push(u)}const L={trace(o,...r){i.trace(o,...r),s("trace",o,...r)},debug(o,...r){i.debug(o,...r),s("debug",o,...r)},info(o,...r){i.info(o,...r),s("info",o,...r)},warn(o,...r){i.warn(o,...r),s("warn",o,...r)},error(o,...r){i.error(o,...r),s("error",`${o}`,...r)},capture(){const o=f.join(`
`);return f.length=0,i.clear(),o}},a=new Map,c=d.window.createOutputChannel("Kiro - MCP Logs",{log:!0});function l(o,r,t,...n){const p=n.map(g=>typeof g=="object"?JSON.stringify(g):g).join(" "),u=new Date().toISOString(),e=t||"KIRO_MCP_DEFAULT",w=`[${u}] [${r}] [${e}] ${o} ${p}`,$=a.get(e)||[];$.push(w),a.set(e,$)}const O={trace(o,r,...t){const n=r?`[${r}] `:"";c.trace(`${n}${o}`,...t),l(o,"trace",r,...t)},debug(o,r,...t){const n=r?`[${r}] `:"";c.debug(`${n}${o}`,...t),l(o,"debug",r,...t)},info(o,r,...t){const n=r?`[${r}] `:"";c.info(`${n}${o}`,...t),l(o,"info",r,...t)},warn(o,r,...t){const n=r?`[${r}] `:"";c.warn(`${n}${o}`,...t),l(o,"warn",r,...t)},error(o,r,...t){const n=r?`[${r}] `:"";c.error(`${n}${o}`,...t),l(`${o}`,"error",r,...t)},getLogsForServer(o){return a.get(o)||[]},show(){c.show()},capture(){const o=Array.from(a.values()).flatMap(r=>r).join(`
`);return a.clear(),c.clear(),o}};function b(o){return o instanceof Error&&(o.name.includes("Abort")||o.message.includes("Aborted"))}function y(o){const r=o instanceof Error?o.name:"UnknownError";return b(o)?"AbortedError":r}class P extends Error{}exports.TrustedError=P;exports.isAbortError=b;exports.logger=L;exports.mapUnknownToErrorType=y;exports.mcpLogger=O;
