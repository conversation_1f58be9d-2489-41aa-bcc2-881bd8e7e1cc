"use strict";const i=require("@opentelemetry/api"),s=require("./initialize-Ci0T2sI9.cjs"),d=require("./errors-CP8CRW6V.cjs");require("path");require("os");require("fs");require("vscode");require("node-machine-id");function f(t){return typeof t=="string"?t:typeof t=="boolean"||typeof t=="number"?t.toString():t instanceof Error?{name:t.name,...t instanceof d.TrustedError?{message:t.message}:{}}:t===void 0?"undefined":JSON.stringify(t)}function a(t){return!!t&&typeof t.then=="function"}function h(t,e,u){return S(t).startActiveSpan(e,u)}function q(t,e,u){return S(t).startActiveSpan(`${t}.${e}`,r=>{try{r.setAttributes(s.<PERSON>faultAttributes);const n=u(r);return a(n)?n.then(o=>(r.setStatus({code:i.SpanStatusCode.OK}),o)).catch(o=>{throw r.recordException(f(o)),r.setStatus({code:i.SpanStatusCode.ERROR}),o}).finally(()=>{r.end()}):(r.setStatus({code:i.SpanStatusCode.OK}),r.end(),n)}catch(n){throw r.recordException(f(n)),r.setStatus({code:i.SpanStatusCode.ERROR}),r.end(),n}})}const c={};function S(t){if(t in c)return c[t];const e=i.trace.getTracer(t,s.APPLICATION_VERSION);return s.isInitialized()&&(c[t]=e),e}exports.startActiveSpan=h;exports.withSpan=q;
