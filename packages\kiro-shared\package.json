{"name": "@kiro/shared", "publisher": "kiro", "description": "Shared logic for Ki<PERSON>", "version": "0.1.0", "private": true, "license": "AWS-IPL", "repository": "https://github.com/kiro-team/kiro-extension.git", "homepage": "https://github.com/kiro-team/kiro-extension", "type": "module", "exports": {"require": "./dist/index.cjs", "import": "./dist/index.js"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "rm -rf dist node_modules", "build": "npm run build:types && npm run build:vite", "build:vite": "vite build", "build:types": "tsc", "dev": "vite build --watch", "test": "vitest run"}, "devDependencies": {"@aws-sdk/client-sso-oidc": "^3.723.0", "@kiro/shared-types": "^1.0.0", "@modelcontextprotocol/sdk": "^1.10.2", "@opentelemetry/api": "^1.9.0", "@opentelemetry/context-async-hooks": "^1.30.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.57.0", "@opentelemetry/exporter-trace-otlp-http": "^0.57.0", "@opentelemetry/id-generator-aws-xray": "^1.2.2", "@opentelemetry/propagator-aws-xray": "^1.26.0", "@opentelemetry/resources": "^1.30.0", "@opentelemetry/sdk-metrics": "^1.30.0", "@opentelemetry/sdk-node": "^0.57.0", "@opentelemetry/sdk-trace-base": "^1.30.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@types/axios": "^0.14.0", "@types/chai": "^4.3.19", "@types/chai-as-promised": "^8.0.2", "@types/semver": "^7.7.0", "@types/sinon": "^17.0.3", "@types/sinon-chai": "^3.0.0", "@types/vscode": "^1.94.0", "axios": "^1.8.2", "axios-retry": "^4.5.0", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "esbuild": "^0.25.0", "glob": "^11.0.1", "msw": "^2.7.3", "node-machine-id": "^1.1.12", "promise-deferred": "^2.0.4", "semver": "^7.7.1", "sinon": "~19.0.0", "sinon-chai": "^3.0.0", "typescript": "^5.7.3", "uuid": "^11.0.5", "vite": "^6.1.0", "vite-plugin-dts": "^4.5.0", "vitest": "^3.0.6", "zod": "^3.23.8"}, "dependencies": {"comment-json": "^4.2.5"}}