{"extends": ["./tsconfig.common.json"], "compilerOptions": {"declaration": true, "outDir": "./dist", "sourceMap": true, "declarationMap": true, "skipLibCheck": true, "allowJs": true, "types": ["mocha", "chai", "chai-as-promised", "sinon", "sinon-chai"], "module": "CommonJS", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src", "typings"], "exclude": ["webviews", "packages/continuedev/core/**/*"]}