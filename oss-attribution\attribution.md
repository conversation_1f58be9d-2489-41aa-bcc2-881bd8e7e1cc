| Name                                      | License                 | Repository url                                                               |
| :---------------------------------------- | :---------------------- | :--------------------------------------------------------------------------- |
| @modelcontextprotocol/sdk                 | MIT                     | git+https://github.com/modelcontextprotocol/typescript-sdk.git               |
| @vscode/codicons                          | CC-BY-4.0               | git+https://github.com/microsoft/vscode-codicons.git                         |
| @vscode/ripgrep                           | MIT                     | https://github.com/microsoft/vscode-ripgrep                                  |
| ajv                                       | MIT                     | n/a                                                                          |
| diff                                      | BSD-3-Clause            | git://github.com/kpdecker/jsdiff.git                                         |
| fs-extra                                  | MIT                     | https://github.com/jprichardson/node-fs-extra                                |
| pako                                      | (MIT AND Zlib)          | n/a                                                                          |
| pkce-challenge                            | MIT                     | git+https://github.com/crouchcd/pkce-challenge.git                           |
| shlex                                     | MIT                     | git+https://github.com/rgov/node-shlex.git                                   |
| short-uuid                                | MIT                     | git+https://github.com/oculus42/short-uuid.git                               |
| web-tree-sitter                           | MIT                     | git+https://github.com/tree-sitter/tree-sitter.git                           |
| workerpool                                | Apache-2.0              | git://github.com/josdejong/workerpool.git                                    |
| @aws/codewhisperer-streaming-client       | Apache-2.0              | n/a                                                                          |
| @amzn/codewhisperer-runtime               | n/a                     | n/a                                                                          |
| @aws-sdk/client-sso-oidc                  | Apache-2.0              | https://github.com/aws/aws-sdk-js-v3.git                                     |
| @aws-sdk/client-sts                       | Apache-2.0              | https://github.com/aws/aws-sdk-js-v3.git                                     |
| @aws-sdk/credential-provider-node         | Apache-2.0              | https://github.com/aws/aws-sdk-js-v3.git                                     |
| @aws-sdk/credential-providers             | Apache-2.0              | https://github.com/aws/aws-sdk-js-v3.git                                     |
| @continuedev/extension                    | AWS-IPL                 | https://github.com/continuedev/continue                                      |
| @inquirer/prompts                         | MIT                     | https://github.com/SBoudrias/Inquirer.js.git                                 |
| @kiro/shared                              | AWS-IPL                 | n/a                                                                          |
| @kiro/shared-types                        | n/a                     | n/a                                                                          |
| @kiro/webview-components                  | AWS-IPL                 | n/a                                                                          |
| @langchain/anthropic                      | MIT                     | **************:langchain-ai/langchainjs.git                                  |
| @langchain/aws                            | MIT                     | **************:langchain-ai/langchainjs.git                                  |
| @langchain/core                           | MIT                     | **************:langchain-ai/langchainjs.git                                  |
| @langchain/langgraph                      | MIT                     | **************:langchain-ai/langgraphjs.git                                  |
| @langchain/ollama                         | MIT                     | **************:langchain-ai/langchainjs.git                                  |
| @langchain/openai                         | MIT                     | **************:langchain-ai/langchainjs.git                                  |
| @npmcli/package-json                      | ISC                     | git+https://github.com/npm/package-json.git                                  |
| @opentelemetry/api                        | Apache-2.0              | n/a                                                                          |
| @opentelemetry/context-async-hooks        | Apache-2.0              | n/a                                                                          |
| @opentelemetry/exporter-metrics-otlp-http | Apache-2.0              | n/a                                                                          |
| @opentelemetry/exporter-trace-otlp-http   | Apache-2.0              | n/a                                                                          |
| @opentelemetry/id-generator-aws-xray      | Apache-2.0              | n/a                                                                          |
| @opentelemetry/propagator-aws-xray        | Apache-2.0              | n/a                                                                          |
| @opentelemetry/resources                  | Apache-2.0              | n/a                                                                          |
| @opentelemetry/sdk-metrics                | Apache-2.0              | n/a                                                                          |
| @opentelemetry/sdk-node                   | Apache-2.0              | n/a                                                                          |
| @opentelemetry/sdk-trace-base             | Apache-2.0              | n/a                                                                          |
| @opentelemetry/semantic-conventions       | Apache-2.0              | n/a                                                                          |
| @smithy/config-resolver                   | Apache-2.0              | https://github.com/awslabs/smithy-typescript.git                             |
| @smithy/node-config-provider              | Apache-2.0              | https://github.com/awslabs/smithy-typescript.git                             |
| @tsconfig/node20                          | MIT                     | https://github.com/tsconfig/bases.git                                        |
| @types/axios                              | MIT                     | n/a                                                                          |
| @types/chai                               | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/chai-as-promised                   | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/fs-extra                           | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/html-escaper                       | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/mocha                              | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/mock-fs                            | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/node                               | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/npmcli__package-json               | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/pako                               | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/promise-deferred                   | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/react                              | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/react-dom                          | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/react-syntax-highlighter           | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/sinon                              | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/sinon-chai                         | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/tar                                | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/unzipper                           | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @types/vscode                             | MIT                     | https://github.com/DefinitelyTyped/DefinitelyTyped.git                       |
| @typescript-eslint/eslint-plugin          | MIT                     | https://github.com/typescript-eslint/typescript-eslint.git                   |
| @typescript-eslint/parser                 | MIT                     | https://github.com/typescript-eslint/typescript-eslint.git                   |
| @unit-mesh/treesitter-artifacts           | MIT                     | https://github.com/unit-mesh/treesitter-artifacts                            |
| @vscode/l10n                              | MIT                     | https://github.com/Microsoft/vscode-l10n.git                                 |
| @vscode/l10n-dev                          | MIT                     | https://github.com/Microsoft/vscode-l10n.git                                 |
| @vscode/test-cli                          | MIT                     | git+https://github.com/Microsoft/vscode-test-cli.git                         |
| @vscode/test-electron                     | MIT                     | https://github.com/Microsoft/vscode-test.git                                 |
| @vscode/vsce                              | MIT                     | https://github.com/Microsoft/vsce                                            |
| async-sema                                | MIT                     | git+https://github.com/vercel/async-sema.git                                 |
| autoprefixer                              | MIT                     | n/a                                                                          |
| axios                                     | MIT                     | https://github.com/axios/axios.git                                           |
| chai                                      | MIT                     | https://github.com/chaijs/chai                                               |
| chai-as-promised                          | WTFPL                   | n/a                                                                          |
| commander                                 | MIT                     | git+https://github.com/tj/commander.js.git                                   |
| consola                                   | MIT                     | n/a                                                                          |
| cross-env                                 | MIT                     | https://github.com/kentcdodds/cross-env.git                                  |
| crypto                                    | ISC                     | git+https://github.com/npm/deprecate-holder.git                              |
| dedent                                    | MIT                     | https://github.com/dmnd/dedent                                               |
| esbuild                                   | MIT                     | git+https://github.com/evanw/esbuild.git                                     |
| esbuild-style-plugin                      | ISC                     | git+https://github.com/g45t345rt/esbuild-style-plugin                        |
| eslint                                    | MIT                     | n/a                                                                          |
| eslint-config-prettier                    | MIT                     | n/a                                                                          |
| eslint-formatter-compact                  | MIT                     | n/a                                                                          |
| eslint-plugin-check-file                  | Apache-2.0              | https://github.com/dukeluo/eslint-plugin-check-file                          |
| eslint-plugin-jsdoc                       | BSD-3-Clause            | git+https://github.com/gajus/eslint-plugin-jsdoc.git                         |
| eslint-plugin-n                           | MIT                     | git+https://github.com/eslint-community/eslint-plugin-n.git                  |
| eslint-plugin-no-only-tests               | MIT                     | **************:levibuzolic/eslint-plugin-no-only-tests.git                   |
| eslint-plugin-prettier                    | MIT                     | n/a                                                                          |
| eslint-plugin-promise                     | ISC                     | https://github.com/eslint-community/eslint-plugin-promise                    |
| eslint-plugin-react                       | MIT                     | https://github.com/jsx-eslint/eslint-plugin-react                            |
| eslint-plugin-react-hooks                 | MIT                     | https://github.com/facebook/react.git                                        |
| eslint-plugin-spellcheck                  | MIT                     | https://github.com/aotaduy/eslint-plugin-spellcheck.git                      |
| fancy-log                                 | MIT                     | n/a                                                                          |
| fast-xml-parser                           | MIT                     | https://github.com/NaturalIntelligence/fast-xml-parser                       |
| globals                                   | MIT                     | n/a                                                                          |
| gray-matter                               | MIT                     | n/a                                                                          |
| gulp                                      | MIT                     | n/a                                                                          |
| html-escaper                              | MIT                     | https://github.com/WebReflection/html-escaper.git                            |
| husky                                     | MIT                     | git+https://github.com/typicode/husky.git                                    |
| jsdom                                     | MIT                     | git+https://github.com/jsdom/jsdom.git                                       |
| jsdom-global                              | MIT                     | git+https://github.com/rstacruz/jsdom-global.git                             |
| license-report                            | MIT                     | https://github.com/kessler/license-report                                    |
| lint-staged                               | MIT                     | n/a                                                                          |
| lucide-react                              | ISC                     | https://github.com/lucide-icons/lucide.git                                   |
| minimatch                                 | ISC                     | git://github.com/isaacs/minimatch.git                                        |
| mocha                                     | MIT                     | https://github.com/mochajs/mocha.git                                         |
| mocha-junit-reporter                      | MIT                     | https://github.com/michaelleeallen/mocha-junit-reporter                      |
| mock-fs                                   | MIT                     | git://github.com/tschaub/mock-fs.git                                         |
| ncp                                       | MIT                     | https://github.com/AvianFlu/ncp.git                                          |
| node-machine-id                           | MIT                     | **************:automation-stack/node-machine-id.git                          |
| npm-run-all                               | MIT                     | n/a                                                                          |
| package-json-type                         | MIT                     | git+https://github.com/ajaxlab/package-json-type.git                         |
| postcss                                   | MIT                     | n/a                                                                          |
| prettier                                  | MIT                     | n/a                                                                          |
| promise-deferred                          | MIT                     | git://github.com/ljharb/promise-deferred.git                                 |
| react                                     | MIT                     | https://github.com/facebook/react.git                                        |
| react-dom                                 | MIT                     | https://github.com/facebook/react.git                                        |
| react-markdown                            | MIT                     | n/a                                                                          |
| react-syntax-highlighter                  | MIT                     | git+https://github.com/react-syntax-highlighter/react-syntax-highlighter.git |
| recharts                                  | MIT                     | git+https://github.com/recharts/recharts.git                                 |
| sinon                                     | BSD-3-Clause            | http://github.com/sinonjs/sinon.git                                          |
| sinon-chai                                | (BSD-2-Clause OR WTFPL) | n/a                                                                          |
| source-map-support                        | MIT                     | https://github.com/evanw/node-source-map-support                             |
| styled-components                         | MIT                     | git+https://github.com/styled-components/styled-components.git               |
| tailwindcss                               | MIT                     | n/a                                                                          |
| tar                                       | ISC                     | https://github.com/isaacs/node-tar.git                                       |
| tree-sitter-cli                           | MIT                     | https://github.com/tree-sitter/tree-sitter.git                               |
| tree-sitter-javascript                    | MIT                     | n/a                                                                          |
| tree-sitter-typescript                    | MIT                     | n/a                                                                          |
| tsx                                       | MIT                     | n/a                                                                          |
| typescript                                | Apache-2.0              | https://github.com/microsoft/TypeScript.git                                  |
| typescript-eslint                         | MIT                     | https://github.com/typescript-eslint/typescript-eslint.git                   |
| typescript-json-schema                    | BSD-3-Clause            | **************:YousefED/typescript-json-schema.git                           |
| unzipper                                  | MIT                     | https://github.com/ZJONSSON/node-unzipper.git                                |
| uuid                                      | MIT                     | https://github.com/uuidjs/uuid.git                                           |
| vscode-uri                                | MIT                     | git+https://github.com/microsoft/vscode-uri.git                              |
| zod                                       | MIT                     | git+https://github.com/colinhacks/zod.git                                    |
| zod-stream                                | MIT                     | git+https://github.com/hack-dance/island-ai.git                              |
| zod-to-json-schema                        | ISC                     | https://github.com/StefanTerdell/zod-to-json-schema                          |

