# 设计文档

## 概述

本设计文档描述了如何创建一个全面的Kiro Agent使用指南，涵盖安装、配置、使用和高级功能。该指南将帮助用户充分利用Kiro Agent的AI辅助开发能力，特别是自定义Anthropic API配置和规范驱动开发模式。

## ⚠️ 重要声明

**当前日期：2025年7月28日**

本指南中的Claude模型信息可能已过时。Anthropic定期发布新模型版本，因此：

1. **所有模型名称和版本号仅供参考**
2. **配置前必须查看官方文档获取最新信息**
3. **上下文长度、性能特性等参数可能已有重大更新**
4. **API调用格式可能已发生变化**

请始终以Anthropic官方文档为准：https://docs.anthropic.com/

## 架构

### 文档结构

指南将采用分层结构，从基础到高级逐步深入：

```
Kiro Agent 使用指南/
├── 1. 快速开始
│   ├── 系统要求
│   ├── 安装步骤
│   └── 首次配置
├── 2. 基础配置
│   ├── 工作区设置
│   ├── 模型配置
│   └── API密钥管理
├── 3. Anthropic API 自定义配置
│   ├── 环境变量配置
│   ├── 配置文件设置
│   ├── 模型选择
│   └── 故障排除
├── 4. 规范驱动开发
│   ├── 概念介绍
│   ├── 工作流程
│   ├── 实践示例
│   └── 最佳实践
├── 5. 高级功能
│   ├── Hooks 系统
│   ├── Steering 配置
│   ├── MCP 集成
│   └── 自定义扩展
└── 6. 参考资料
    ├── 命令参考
    ├── 快捷键
    ├── 配置选项
    └── 常见问题
```

## 组件和接口

### 1. 安装和设置组件

**功能：** 指导用户完成Kiro Agent的安装和初始设置

**关键信息：**
- 系统要求：VS Code 1.94.0+, Node.js 20.18.1+
- 安装方式：从源码构建（当前为开源项目）
- 依赖项：npm, git

**安装和运行步骤：**

1. **克隆和准备项目：**
```bash
# 克隆仓库
git clone https://github.com/ghuntley/amazon-kiro.kiro-agent-source-code-analysis.git
cd amazon-kiro.kiro-agent-source-code-analysis

# 安装依赖
npm install

# 构建扩展
npm run compile
```

2. **创建VS Code调试配置：**
   项目需要调试配置文件才能正确运行扩展。创建以下文件：

   **`.vscode/launch.json`：**
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Run Extension",
         "type": "extensionHost",
         "request": "launch",
         "args": [
           "--extensionDevelopmentPath=${workspaceFolder}"
         ],
         "outFiles": [
           "${workspaceFolder}/dist/**/*.js"
         ],
         "preLaunchTask": "${workspaceFolder}/npm: compile"
       }
     ]
   }
   ```

   **`.vscode/tasks.json`：**
   ```json
   {
     "version": "2.0.0",
     "tasks": [
       {
         "type": "npm",
         "script": "compile",
         "group": "build",
         "presentation": {
           "panel": "shared",
           "showReuseMessage": false,
           "clear": false
         },
         "problemMatcher": "$tsc"
       }
     ]
   }
   ```

3. **在VS Code中运行扩展：**
   - 用VS Code打开项目文件夹
   - 按 `F5` 键（或者点击菜单 Run > Start Debugging）
   - 现在应该会直接启动 "Run Extension" 配置
   - 这会启动一个新的VS Code窗口，标题为 "[Extension Development Host]"
   - 在这个新窗口中，Kiro Agent扩展就已经加载并运行了

4. **验证扩展是否正常工作：**
   - 在新窗口中，你应该能看到Kiro的侧边栏图标
   - 打开命令面板 (Ctrl+Shift+P)，搜索 "Kiro" 应该能看到相关命令
   - 侧边栏应该显示Chat和Kiro两个面板

5. **开始使用：**
   - 点击Chat面板开始与AI对话
   - 在Kiro面板中可以管理Specs、Hooks、Steering等功能

**重要说明：**
- 按F5启动的是开发模式，用于测试扩展
- 如果要正式安装，需要打包成.vsix文件：`npm run package`
- 然后通过VS Code的扩展管理器安装.vsix文件

### 2. Anthropic API 配置组件

**功能：** 详细说明如何配置自定义Anthropic API设置

**配置方法：**

#### 方法1：环境变量配置
```bash
# 设置自定义API基础URL
export ANTHROPIC_BASE_URL="https://your-custom-api.example.com/v1"

# 设置API密钥
export ANTHROPIC_API_KEY="your-api-key-here"
```

#### 获取最新模型信息（必读）
**更新：** 截至2025年7月28日，已确认Claude 4模型信息。但仍建议定期检查是否有更新版本：

- **Anthropic官方文档：** https://docs.anthropic.com/claude/docs/models-overview
- **模型发布公告：** https://www.anthropic.com/news  
- **API参考：** https://docs.anthropic.com/claude/reference/
- **定价信息：** https://www.anthropic.com/pricing

**检查要点：**
1. 最新的模型名称和版本号
2. 上下文长度限制（可能已超过200k tokens）
3. 新模型的性能特点和适用场景
4. API调用格式是否有变化
5. 定价结构更新

#### 方法2：MCP配置文件
基于项目分析，Kiro使用MCP（Model Context Protocol）来管理外部服务配置。

**工作区级配置：** `.kiro/settings/mcp.json`
```json
{
  "mcpServers": {
    "custom-anthropic": {
      "command": "uvx",
      "args": ["custom-anthropic-server@latest"],
      "env": {
        "ANTHROPIC_BASE_URL": "https://your-custom-api.example.com/v1",
        "ANTHROPIC_API_KEY": "your-api-key-here",
        "ANTHROPIC_MODEL": "claude-opus-4-0"
      },
      "disabled": false,
      "autoApprove": ["anthropic-chat", "anthropic-completion"]
    }
  }
}
```

**用户级配置：** `~/.kiro/settings/mcp.json`
```json
{
  "mcpServers": {
    "global-anthropic": {
      "command": "uvx", 
      "args": ["anthropic-mcp-server@latest"],
      "env": {
        "ANTHROPIC_API_KEY": "your-global-api-key"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

#### 方法3：VS Code设置
通过VS Code的设置界面配置：
1. 打开命令面板 (Ctrl+Shift+P)
2. 搜索 "Kiro: Open MCP Config"
3. 选择工作区或用户级配置
4. 添加Anthropic配置

### 3. 模型选择和配置

**最新更新：** 截至2025年7月28日，已确认Claude 4模型为最新版本（2025年5月14日发布）。

**最新Claude 4模型（2025年5月14日发布）：**

| 模型别名 | 完整模型ID | 推荐优先级 | 说明 |
|---------|-----------|-----------|------|
| claude-opus-4-0 | claude-opus-4-20250514 | 1 (优先推荐) | 最新最强性能模型 |
| claude-sonnet-4-0 | claude-sonnet-4-20250514 | 2 (次选推荐) | 最新高性能模型 |

**使用建议：**
- 可以使用简短别名（如 `claude-opus-4-0`）或完整ID（如 `claude-opus-4-20250514`）
- 优先选择 Claude Opus 4 获得最佳性能
- Claude Sonnet 4 提供良好的性能和成本平衡

**历史Claude 3模型（仍可使用但不推荐）：**
- claude-3-opus-20240229 (历史最强性能模型)
- claude-3-5-sonnet-20240620 (历史高性能模型)
- claude-3-sonnet-20240229 (历史平衡性能模型)
- claude-3-haiku-20240307 (历史快速响应模型)

**获取最新模型信息的必要步骤：**
1. 访问 https://docs.anthropic.com/claude/docs/models-overview
2. 查看最新的模型版本和发布日期
3. 确认模型的上下文长度限制（可能已超过200k）
4. 了解新模型的特性和性能改进

**配置示例（使用最新Claude 4模型）：**
```json
{
  "model": "claude-opus-4-0",
  "maxTokens": 4096,
  "temperature": 0.7,
  "contextLength": 200000,
  "apiBase": "https://api.anthropic.com/v1/"
}
```

**模型选择配置：**
```json
// 最佳性能配置
{
  "model": "claude-opus-4-0",  // 或使用完整ID: claude-opus-4-20250514
  "contextLength": 200000
}

// 平衡性能配置  
{
  "model": "claude-sonnet-4-0", // 或使用完整ID: claude-sonnet-4-20250514
  "contextLength": 200000
}
```

**重要配置说明：**
- `model`: 推荐使用 `claude-opus-4-0` 获得最佳性能
- `contextLength`: 设置为200000，Claude 4可能支持更大上下文窗口
- `maxTokens`: 单次响应的最大token数，建议4096-8192
- `apiBase`: 使用标准Anthropic API端点

### 4. 规范驱动开发工作流

**工作流程：**
1. **需求收集** → `requirements.md`
2. **设计文档** → `design.md`  
3. **任务规划** → `tasks.md`
4. **任务执行** → 逐步实现

**文件结构：**
```
.kiro/specs/your-feature/
├── requirements.md  # EARS格式需求
├── design.md       # 技术设计
└── tasks.md        # 实现任务列表
```

### 5. 高级功能配置

#### Steering系统
**位置：** `.kiro/steering/*.md`

**示例配置：**
```markdown
---
inclusion: always
---

# 项目编码规范

## 代码风格
- 使用TypeScript严格模式
- 遵循ESLint规则
- 优先使用函数式编程
```

#### Hooks系统
**配置文件：** `.hooks/*.json`

**示例Hook：**
```json
{
  "hooks": [{
    "type": "FileEditedHook",
    "filePattern": "*.ts",
    "action": {
      "type": "AskAgentHook", 
      "message": "检查这个TypeScript文件的类型安全性"
    }
  }]
}
```

## 数据模型

### 配置数据模型

```typescript
interface KiroConfig {
  anthropic?: {
    apiKey: string;
    baseUrl?: string;
    model: string;
    maxTokens?: number;
    contextLength?: number; // 推荐设置为200000
    temperature?: number;
  };
  mcp?: {
    servers: Record<string, MCPServerConfig>;
  };
  steering?: {
    files: string[];
    inclusion: 'always' | 'conditional' | 'manual';
  };
  hooks?: {
    enabled: boolean;
    definitions: HookDefinition[];
  };
}

interface MCPServerConfig {
  command: string;
  args: string[];
  env: Record<string, string>;
  disabled: boolean;
  autoApprove: string[];
}
```

### 规范数据模型

```typescript
interface SpecDocument {
  requirements: {
    userStories: UserStory[];
    acceptanceCriteria: AcceptanceCriteria[];
  };
  design: {
    overview: string;
    architecture: string;
    components: Component[];
  };
  tasks: {
    items: TaskItem[];
    status: 'not_started' | 'in_progress' | 'completed';
  };
}
```

## 错误处理

### API配置错误
- **无效API密钥：** 显示清晰错误信息，提供配置指导
- **网络连接问题：** 提供重试机制和故障排除步骤
- **模型不可用：** 列出可用模型，建议替代方案

### 配置文件错误
- **JSON格式错误：** 提供语法检查和修复建议
- **路径错误：** 验证文件路径，提供正确路径示例
- **权限问题：** 检查文件权限，提供解决方案

### 扩展运行错误
- **F5无响应：** 检查是否在正确的项目根目录，确保package.json存在
- **编译错误：** 运行 `npm run compile` 检查构建错误
- **依赖缺失：** 重新运行 `npm install`
- **端口占用：** 关闭其他Extension Development Host窗口

### 工作流错误
- **规范文件缺失：** 自动创建模板文件
- **任务执行失败：** 提供详细错误日志和恢复选项
- **依赖冲突：** 检测并解决依赖问题

### 常见问题解答
- **Q: 按F5后出现多个选择选项？**
  A: 这说明项目缺少调试配置文件。需要创建 `.vscode/launch.json` 和 `.vscode/tasks.json` 文件（参见上面的配置示例）。创建后再按F5就会直接运行扩展。

- **Q: 按F5后没有反应？**
  A: 确保在VS Code中打开了项目根目录，并且已经运行了 `npm install` 和 `npm run compile`

- **Q: Extension Development Host窗口中看不到Kiro？**
  A: 检查控制台是否有错误信息，可能需要重新构建项目

- **Q: 如何查看可用的调试配置？**
  A: 点击侧边栏的"运行和调试"图标，在下拉菜单中查看所有可用配置

- **Q: 如何正式安装扩展而不是开发模式？**
  A: 运行 `npm run package` 生成.vsix文件，然后在VS Code中安装该文件

## 测试策略

### 文档测试
- **准确性验证：** 确保所有配置示例可以正常工作
- **完整性检查：** 验证所有功能都有相应文档
- **可用性测试：** 让新用户按照指南操作，收集反馈

### 配置测试
- **API连接测试：** 验证自定义API配置是否正常工作
- **模型切换测试：** 确保可以正常切换不同模型
- **工作流测试：** 验证规范驱动开发流程的完整性

### 集成测试
- **VS Code集成：** 确保扩展在不同VS Code版本中正常工作
- **跨平台测试：** 验证在Windows、macOS、Linux上的兼容性
- **性能测试：** 确保配置更改不影响扩展性能