"use strict";const u=require("path"),g=require("os"),p=require("fs");function r(t){const o=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(t){for(const e in t)if(e!=="default"){const n=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(o,e,n.get?n:{enumerable:!0,get:()=>t[e]})}}return o.default=t,Object.freeze(o)}const c=r(u),h=r(g),s=r(p);function a(){const t=h.homedir();return c.join(t,".kiro")}function f(t){return c.join(t,".kiro")}function m(t){let o,e;if(t){const i=c.join(f(t),"settings","mcp.json");s.existsSync(i)&&(o=i)}const n=a();if(n){const i=c.join(n,"settings","mcp.json");s.existsSync(i)&&(e=i)}return{workspaceConfigPath:o,userConfigPath:e}}exports.getActiveMcpConfigLocation=m;exports.getHomeKiroPath=a;exports.getWorkspaceKiroPath=f;
